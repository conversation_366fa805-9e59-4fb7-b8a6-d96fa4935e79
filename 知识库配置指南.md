# Dify知识库配置指南

## 🎯 **确保AI每次都读取模板的配置方法**

### **方法1：知识库文档命名策略**

#### **文档命名规范**
```
1. 标准公文模板.docx (原模板.docx)
2. 述职报告模板.docx (原模板1.docx)
3. 格式要求说明.txt
4. HTML输出规范.txt
```

#### **重命名原因**
- 明确的文件名提高检索准确性
- 避免模糊的"模板"、"模板1"命名
- 便于AI理解文档用途

### **方法2：创建格式要求文档**

创建一个专门的格式要求文档，上传到知识库：

```markdown
# 公文格式要求文档

## 标准公文格式要求（基于模板.docx）

### 版头部分
- 份号：6位数字，3号仿宋体，顶格左上角
- 密级：××★保密期限，3号黑体，顶格左上角  
- 紧急程度：特急/急件/平件，3号黑体，顶格左上角
- 发文机关标志：小标宋体，红色，居中，根据字数调整字号
- 发文字号：3号仿宋体，居中
- 签发人：3号仿宋体，右对齐

### 主体部分
- 标题：2号小标宋体，居中，粗体
- 主送机关：3号仿宋体，顶格
- 正文：3号仿宋体，首行缩进2字符
- 发文机关署名：3号仿宋体，右对齐
- 成文日期：3号仿宋体，右对齐

### 版记部分
- 抄送机关：4号仿宋体，左空1字符
- 印发机关和印发日期：4号仿宋体

## 述职报告格式要求（基于模板1.docx）

### 文档结构
- 标题：黑体，18pt，居中，粗体
- 称谓：仿宋体，16pt，左对齐
- 层次序号：一、（一）、1.、（1）
- 正文：仿宋体，16pt，首行缩进2字符
- 署名：仿宋体，16pt，左对齐
- 日期：仿宋体，16pt，左对齐

## HTML输出对应关系

### 标准公文HTML类名
- fenhao：份号
- miji：密级
- jinji：紧急程度
- jiguan：发文机关标志
- fawenhao：发文字号
- qianfaren：签发人
- title：标题
- zhusong：主送机关
- content：正文
- signature：署名
- date：日期
- chaosong：抄送
- yinfa：印发

### 述职报告HTML类名
- title：标题
- greeting：称谓
- level1：一级标题
- level2：二级标题
- level3：三级标题
- content：正文内容
- signature：署名
- date：日期
```

### **方法3：知识库检索优化设置**

#### **在Dify中的具体配置**

1. **知识库创建**
   ```
   知识库名称：公文模板库
   描述：包含标准公文和述职报告的格式模板
   ```

2. **文档上传顺序**
   ```
   1. 格式要求说明.txt (优先级最高)
   2. 标准公文模板.docx
   3. 述职报告模板.docx  
   4. HTML输出规范.txt
   ```

3. **检索参数设置**
   ```
   相关性阈值：0.6 (降低阈值，增加召回)
   Top K：5 (增加检索数量)
   重排序：开启
   ```

4. **关键词优化**
   ```
   在格式要求文档中添加关键词：
   - 公文、通知、请示、报告、决定
   - 述职、述廉、工作总结
   - HTML、格式、模板、标准
   ```

### **方法4：提示词中的强制检索**

在用户提示词模板中添加：

```
## 开始生成前的必要步骤：
1. **检索模板格式**：查找"标准公文模板"或"述职报告模板"的格式要求
2. **确认文档类型**：根据用户需求判断是公文还是报告
3. **选择对应模板**：使用相应的HTML格式结构
4. **验证格式完整性**：确保所有必要要素都包含

## 强制检索关键词：
- 如果是公文类：搜索"标准公文模板 格式要求 HTML"
- 如果是报告类：搜索"述职报告模板 格式要求 HTML"
```

### **方法5：工作流节点配置**

如果使用Dify工作流模式：

```
节点1：知识库检索
- 查询：根据用户输入的文档类型检索对应模板
- 输出：模板格式要求

节点2：内容生成  
- 输入：用户需求 + 模板格式要求
- 模型：GPT-4或其他大模型
- 输出：符合模板格式的HTML内容

节点3：格式验证
- 检查：HTML标签完整性
- 验证：内容要素完整性
- 输出：最终的HTML格式文档
```

### **方法6：测试验证方法**

#### **测试检索效果**
```
测试查询1："标准公文格式要求"
期望结果：返回模板.docx相关内容

测试查询2："述职报告格式"  
期望结果：返回模板1.docx相关内容

测试查询3："HTML输出格式"
期望结果：返回HTML格式规范
```

#### **验证AI是否读取模板**
```
在AI输出前询问：
"请先说明你将使用哪个模板格式，并简述该模板的主要特点"

正确回答应包含：
- 明确的模板选择（标准公文/述职报告）
- 模板的主要格式特点
- 对应的HTML结构说明
```

### **方法7：知识库内容优化**

#### **创建模板说明文档**
```markdown
# 模板使用说明

## 何时使用标准公文模板
- 通知、请示、报告、决定、公告等正式公文
- 包含发文机关、主送机关、抄送等要素
- 需要版头、主体、版记完整结构

## 何时使用述职报告模板  
- 个人工作总结、述职述廉报告
- 年度汇报、履职情况说明
- 个人工作回顾和计划

## 模板选择决策树
用户需求 → 关键词识别 → 模板选择 → HTML格式应用
```

### **推荐配置方案**

**最佳实践组合：**
1. 使用方法1（强制引用）+ 方法2（格式要求文档）
2. 降低检索阈值到0.6，增加Top K到5
3. 在系统提示词中明确要求参考模板
4. 创建专门的格式要求说明文档
5. 定期测试验证检索效果

这样配置可以确保AI每次都能准确读取到模板信息！🎯
