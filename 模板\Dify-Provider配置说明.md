# Dify Provider 配置说明

## 📋 **配置概述**

这个Provider模板已经配置为连接您的Dify API系统，支持三种不同的AI服务：

### **🎯 支持的模型/服务**

1. **文件解读** (`file-interpretation` 或 `文件解读`)
   - 工作流ID: `a6NzXcb7rLS3Eo76`
   - 功能: 文件内容分析和解读
   - API端点: `/workflows/a6NzXcb7rLS3Eo76/run`

2. **公文写作** (`document-generation` 或 `公文写作`)
   - 工作流ID: `prHddcqyPKtVcPiY`
   - 功能: 标准公文和报告生成
   - API端点: `/workflows/prHddcqyPKtVcPiY/run`

3. **通用聊天** (`general-chat` 或 `聊天`)
   - 应用ID: `e7afaa18-1258-4370-ad2b-de107155aa85`
   - 功能: 通用AI聊天助手
   - API端点: `/chat-messages`

## ⚙️ **配置要求**

### **1. API密钥配置**

您需要在Provider中设置正确的API密钥。在`getHeaders`方法中更新以下部分：

```javascript
// 文件解读工作流
headers["Authorization"] = "Bearer app-your-file-interpretation-key";

// 公文写作工作流  
headers["Authorization"] = "Bearer app-your-document-generation-key";

// 通用聊天应用
headers["Authorization"] = "Bearer app-your-general-chat-key";
```

### **2. 基础URL配置**

当前配置的基础URL是 `http://localhost/v1`，如果您的Dify服务在不同地址，请修改构造函数：

```javascript
constructor() {
    super("Dify", "http://your-dify-server/v1", "", "v1");
}
```

## 🔧 **API请求格式**

### **工作流请求格式**
```json
{
    "inputs": {
        "query": "用户输入的内容"
    },
    "response_mode": "blocking",
    "user": "onlyoffice-user"
}
```

### **应用请求格式**
```json
{
    "inputs": {},
    "query": "用户输入的内容", 
    "response_mode": "blocking",
    "conversation_id": "",
    "user": "onlyoffice-user"
}
```

## 📝 **使用方法**

### **在OnlyOffice中使用**

1. **安装Provider**
   - 将`providerTemplate.js`放入OnlyOffice AI插件的providers目录
   - 重启OnlyOffice服务

2. **选择模型**
   - 在AI插件中选择"Dify"作为提供商
   - 选择对应的模型：
     - `文件解读` - 用于分析文档内容
     - `公文写作` - 用于生成标准公文
     - `聊天` - 用于通用对话

3. **配置API密钥**
   - 在AI插件设置中输入对应的Dify API密钥

### **功能示例**

#### **公文写作示例**
```
用户输入: "请生成一份关于加强网络安全管理的通知"
模型选择: 公文写作
预期输出: 标准格式的HTML公文内容
```

#### **文件解读示例**
```
用户输入: "请分析这份文档的主要内容"
模型选择: 文件解读  
预期输出: 文档内容的详细分析
```

## 🔍 **调试和测试**

### **测试步骤**

1. **检查API连接**
   ```bash
   curl -X POST http://localhost/v1/workflows/prHddcqyPKtVcPiY/run \
   -H "Authorization: Bearer your-api-key" \
   -H "Content-Type: application/json" \
   -d '{"inputs":{"query":"测试"},"response_mode":"blocking","user":"test"}'
   ```

2. **验证响应格式**
   - 确保Dify返回的响应格式与OnlyOffice AI插件兼容
   - 检查是否需要额外的响应处理

3. **测试不同模型**
   - 分别测试三种不同的模型配置
   - 验证API密钥和端点URL的正确性

## ⚠️ **注意事项**

### **安全考虑**
1. **API密钥保护**: 不要在代码中硬编码API密钥
2. **HTTPS使用**: 生产环境建议使用HTTPS
3. **访问控制**: 限制API访问权限

### **性能优化**
1. **请求超时**: 设置合理的请求超时时间
2. **错误处理**: 添加完善的错误处理机制
3. **缓存策略**: 考虑实现响应缓存

### **兼容性**
1. **OnlyOffice版本**: 确保与您的OnlyOffice版本兼容
2. **Dify版本**: 验证与Dify API版本的兼容性
3. **浏览器支持**: 测试不同浏览器的兼容性

## 🚀 **扩展功能**

### **可能的扩展**

1. **添加更多工作流**
   - 在`checkModelCapability`方法中添加新的模型配置
   - 为每个工作流设置专门的API密钥

2. **自定义响应处理**
   - 重写响应处理方法以适配特殊格式
   - 添加响应数据转换逻辑

3. **用户界面优化**
   - 添加模型选择的用户友好界面
   - 提供配置向导

## 📞 **技术支持**

如果在配置过程中遇到问题：

1. 检查Dify服务是否正常运行
2. 验证API密钥是否正确
3. 查看OnlyOffice和Dify的日志文件
4. 确认网络连接和防火墙设置

配置完成后，您就可以在OnlyOffice中直接使用Dify的AI服务进行公文写作和文档分析了！🎯
