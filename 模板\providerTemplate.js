/*
 * (c) Copyright Ascensio System SIA 2010-2025
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-6 Ernesta Birznieka-Upish
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */

"use strict";

class Provider extends AI.Provider {

	constructor() {
		// 配置Dify API
		super("Dify", "http://localhost/v1", "", "v1");
	}

	checkExcludeModel() {
		// Dify工作流不需要排除特定模型
		return false;
	}

	checkModelCapability(model) {
		// 配置Dify工作流模型

		// 文件解读工作流
		if ("file-interpretation" === model.id || "文件解读" === model.id) {
			model.options.max_input_tokens = AI.InputMaxTokens["16k"];
			model.endpoints.push(AI.Endpoints.Types.v1.Chat_Completions);
			model.workflow_id = "a6NzXcb7rLS3Eo76";
			model.description = "文件解读和分析";
			return AI.CapabilitiesUI.Chat;
		}

		// 公文写作工作流
		if ("document-generation" === model.id || "公文写作" === model.id) {
			model.options.max_input_tokens = AI.InputMaxTokens["16k"];
			model.endpoints.push(AI.Endpoints.Types.v1.Chat_Completions);
			model.workflow_id = "prHddcqyPKtVcPiY";
			model.description = "标准公文和报告生成";
			return AI.CapabilitiesUI.Chat;
		}

		// 通用聊天应用
		if ("general-chat" === model.id || "聊天" === model.id) {
			model.options.max_input_tokens = AI.InputMaxTokens["16k"];
			model.endpoints.push(AI.Endpoints.Types.v1.Chat_Completions);
			model.app_id = "e7afaa18-1258-4370-ad2b-de107155aa85";
			model.description = "通用AI聊天助手";
			return AI.CapabilitiesUI.Chat;
		}

		// 默认配置
		model.options.max_input_tokens = AI.InputMaxTokens["16k"];
		model.endpoints.push(AI.Endpoints.Types.v1.Chat_Completions);
		return AI.CapabilitiesUI.Chat;
	};

	// 重写请求方法以适配Dify API
	getChatCompletion(messages, model, options) {
		// 根据模型类型构建不同的请求
		if (model.workflow_id) {
			// 工作流请求格式
			return {
				inputs: {
					query: this.getLastUserMessage(messages)
				},
				response_mode: "blocking",
				user: "onlyoffice-user"
			};
		} else if (model.app_id) {
			// 应用请求格式
			return {
				inputs: {},
				query: this.getLastUserMessage(messages),
				response_mode: "blocking",
				conversation_id: "",
				user: "onlyoffice-user"
			};
		}

		// 默认格式
		return super.getChatCompletion(messages, model, options);
	}

	// 获取最后一条用户消息
	getLastUserMessage(messages) {
		for (let i = messages.length - 1; i >= 0; i--) {
			if (messages[i].role === "user") {
				return messages[i].content;
			}
		}
		return "";
	}

	// 重写API端点URL构建
	getEndpointUrl(endpoint, model) {
		if (model.workflow_id) {
			return `${this.baseUrl}/workflows/${model.workflow_id}/run`;
		} else if (model.app_id) {
			return `${this.baseUrl}/chat-messages`;
		}

		return super.getEndpointUrl(endpoint, model);
	}

	// 重写请求头设置
	getHeaders(model) {
		let headers = super.getHeaders(model);

		// 从用户设置中获取API密钥
		// OnlyOffice AI插件会自动将用户输入的API Key传递给Provider
		if (this.apiKey) {
			headers["Authorization"] = "Bearer " + this.apiKey;
		}

		return headers;
	}

	getImageGeneration(message, model) {
		let result = super.getImageGeneration(message, model);
		result.size = result.width + "x" + result.height;
		delete result.width;
		delete result.height;
		return result;
	}

}
