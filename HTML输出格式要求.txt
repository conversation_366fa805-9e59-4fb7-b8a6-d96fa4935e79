HTML输出格式严格要求文档

本文档规定AI输出HTML内容的严格格式要求，必须严格遵循执行。

=== 核心要求 ===

1. 只输出div片段，不要输出完整HTML页面
2. 严格使用指定的class名称，不得自创
3. 绝对不使用任何占位符
4. 内容必须具体详实

=== 标准公文HTML格式 ===

适用场景：通知、请示、报告、决定、公告、通告、意见、办法、函、批复

必须使用的class名称：
- fenhao：份号
- miji：密级  
- jinji：紧急程度
- jiguan：发文机关标志
- fawenhao：发文字号
- qianfaren：签发人
- red-line：红色分隔线
- title：标题
- zhusong：主送机关
- content：正文段落
- signature：发文机关署名
- date：成文日期
- chaosong：抄送机关
- yinfa：印发信息

标准输出示例：
<div class="fenhao">000001</div>
<div class="miji">秘密★1年</div>
<div class="jinji">特急</div>
<div class="jiguan">××市人民政府办公室文件</div>
<div class="fawenhao">市政办〔2024〕15号</div>
<div class="qianfaren">签发人：张三 李四</div>
<div class="red-line">————————————————————————————————</div>
<div class="title">关于加强网络安全管理的通知</div>
<div class="zhusong">各区县政府，市政府各部门：</div>
<div class="content">为切实加强网络安全管理，防范网络安全风险，现就有关事项通知如下：</div>
<div class="content">一、建立健全网络安全管理制度。各单位要制定完善的网络安全管理制度，明确网络安全责任人，建立网络安全检查机制，确保网络安全措施落实到位。</div>
<div class="content">二、加强网络安全技术防护。要安装正版杀毒软件，及时更新系统补丁，设置复杂密码，定期备份重要数据，防止病毒感染和数据丢失。</div>
<div class="content">三、强化网络安全教育培训。要定期组织网络安全知识培训，提高员工网络安全意识，增强防范网络攻击的能力。</div>
<div class="content">各单位要高度重视网络安全工作，认真贯彻执行本通知要求，确保网络安全。</div>
<div class="signature">××市人民政府办公室</div>
<div class="date">2024年12月25日</div>
<div class="chaosong">抄送：市委办公室，市人大常委会办公室，市政协办公室。</div>
<div class="yinfa">××市人民政府办公室 2024年12月25日印发</div>

=== 述职报告HTML格式 ===

适用场景：述职报告、述廉报告、工作总结、个人总结、年度汇报

必须使用的class名称：
- title：标题
- greeting：称谓
- level1：一级标题（一、二、三）
- level2：二级标题（（一）（二）（三））
- level3：三级标题（1.2.3.）
- content：正文内容
- signature：署名
- date：日期

标准输出示例：
<div class="title">2024年度办公室主任述职述廉报告</div>
<div class="greeting">尊敬的各位领导、同事们：</div>
<div class="content">根据组织要求，现将我担任办公室主任一年来的履职情况和廉洁自律情况向大家汇报，请予审议。</div>
<div class="level1">一、2024年度履职情况</div>
<div class="level2">（一）日常管理工作方面</div>
<div class="content">2024年，我认真履行办公室主任职责，统筹协调各项日常工作。全年共处理各类公文368份，组织大小会议42次，接待来访人员156人次，各项工作运转有序高效。</div>
<div class="level2">（二）重点项目推进方面</div>
<div class="content">重点推进了办公自动化系统升级改造项目，投入资金80万元，实现了无纸化办公，提升了工作效率。完成了档案室标准化建设，整理归档文件2万余份。</div>
<div class="level1">二、2024年度廉洁自律情况</div>
<div class="content">严格执行中央八项规定精神，自觉遵守廉洁从政各项规定。全年未收受任何礼品礼金，未参加任何可能影响公正执行公务的宴请活动。</div>
<div class="level1">三、存在的问题和不足</div>
<div class="content">通过深入反思，我认为还存在以下问题：一是理论学习还不够深入；二是创新意识还需加强；三是与基层联系还不够紧密。</div>
<div class="level1">四、2025年工作打算和改进措施</div>
<div class="content">针对存在的问题，我将采取以下改进措施：</div>
<div class="level3">1.加强理论学习，每月至少学习党的理论知识2次，提高政治素养和业务能力。</div>
<div class="level3">2.创新工作方法，积极运用信息化手段，探索新的工作模式。</div>
<div class="level3">3.深入基层调研，每季度至少下基层3次，了解实际情况。</div>
<div class="content">以上是我的述职述廉报告，工作中还有许多不足之处，恳请各位领导和同事批评指正。</div>
<div class="signature">发言人：张建华</div>
<div class="date">日期：2024年12月25日</div>

=== 严禁的错误格式 ===

❌ 错误示例1：完整HTML页面
<!DOCTYPE html>
<html>
<head>...</head>
<body>...</body>
</html>

❌ 错误示例2：自定义样式
<style>
.section-title { font-size: 24px; }
</style>

❌ 错误示例3：自创class名称
<div class="section-title">...</div>
<div class="content-box">...</div>
<div class="list-group">...</div>

❌ 错误示例4：使用占位符
<div class="content">(具体内容)</div>
<div class="content">[详细描述]</div>
<div class="content">××××</div>

=== 质量检查清单 ===

输出前必须检查：
✓ 是否只输出div片段，没有完整HTML结构
✓ 是否使用了正确的class名称
✓ 是否包含任何占位符
✓ 内容是否具体详实
✓ 格式是否符合对应模板要求
✓ HTML标签是否完整闭合

=== 文档类型识别 ===

关键词识别：
- 标准公文：通知、请示、报告、决定、公告、通告、意见、办法、函、批复、关于...的
- 述职报告：述职、述廉、工作总结、个人总结、年度汇报、履职情况

根据关键词自动选择对应的HTML格式进行输出。

本文档是AI输出HTML格式的强制性标准，必须100%严格遵循执行。
