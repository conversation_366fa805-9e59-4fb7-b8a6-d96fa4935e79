{"ast": null, "code": "// OnlyOffice配置文件\nexport const ONLYOFFICE_CONFIG = {\n  // OnlyOffice Document Server的URL\n  documentServerUrl: 'http://localhost:8080',\n  // API脚本URL\n  apiUrl: 'http://localhost:8080/web-apps/apps/api/documents/api.js',\n  // 后端服务配置\n  backendUrl: 'http://localhost:3001',\n  // 文档存储配置\n  documentStorage: {\n    // 文档存储的基础URL（使用后端服务）\n    baseUrl: 'http://localhost:3001/files',\n    // 回调URL（OnlyOffice保存文档时的回调）\n    callbackUrl: 'http://localhost:3001/callback'\n  },\n  // 默认文档配置\n  defaultDocument: {\n    fileType: 'docx',\n    title: '新建文档.docx',\n    permissions: {\n      edit: true,\n      download: true,\n      print: true,\n      review: true,\n      comment: true\n    }\n  },\n  // 编辑器配置\n  editorConfig: {\n    mode: 'edit',\n    lang: 'zh-C<PERSON>',\n    user: {\n      id: 'user-1',\n      name: '用户'\n    },\n    customization: {\n      autosave: true,\n      chat: false,\n      comments: true,\n      help: true,\n      hideRightMenu: false,\n      hideRulers: false,\n      toolbar: true,\n      zoom: 100,\n      // 启用插件支持\n      plugins: true\n    },\n    // 插件配置 - 添加公文排版插件和模板格式化插件\n    plugins: {\n      autostart: ['asc.{OFFICIAL-DOC-FORMATTER-12345678-1234-5678-9ABC-123456789ABC}', 'asc.{TEMPLATE-FORMATTER-OFFICIAL-12345678-1234-5678-9ABC-123456789ABC}'],\n      pluginsData: ['http://localhost:8080/sdkjs-plugins/asc.{OFFICIAL-DOC-FORMATTER-12345678-1234-5678-9ABC-123456789ABC}/config.json', 'http://localhost:8080/sdkjs-plugins/asc.{TEMPLATE-FORMATTER-OFFICIAL-12345678-1234-5678-9ABC-123456789ABC}/config.json']\n    }\n  },\n  // 编辑器尺寸配置\n  width: '100%',\n  height: '100%'\n};\n\n// 生成文档配置的辅助函数\nexport const generateDocumentConfig = (documentUrl, documentKey, title) => {\n  return {\n    documentType: getDocumentType(title),\n    document: {\n      fileType: getFileExtension(title),\n      key: documentKey,\n      title: title,\n      url: documentUrl,\n      permissions: ONLYOFFICE_CONFIG.defaultDocument.permissions\n    },\n    editorConfig: {\n      ...ONLYOFFICE_CONFIG.editorConfig,\n      callbackUrl: ONLYOFFICE_CONFIG.documentStorage.callbackUrl\n    },\n    width: ONLYOFFICE_CONFIG.width,\n    height: ONLYOFFICE_CONFIG.height,\n    events: {\n      onDocumentReady: () => {\n        console.log('OnlyOffice文档已准备就绪');\n      },\n      onDocumentStateChange: event => {\n        console.log('文档状态变化:', event);\n      },\n      onError: event => {\n        console.error('OnlyOffice错误:', event);\n      }\n    }\n  };\n};\n\n// 根据文件名获取文档类型\nfunction getDocumentType(filename) {\n  const ext = getFileExtension(filename);\n  if (['doc', 'docx', 'odt', 'rtf', 'txt'].includes(ext)) {\n    return 'word';\n  } else if (['xls', 'xlsx', 'ods', 'csv'].includes(ext)) {\n    return 'cell';\n  } else if (['ppt', 'pptx', 'odp'].includes(ext)) {\n    return 'slide';\n  }\n  return 'word'; // 默认为word文档\n}\n\n// 获取文件扩展名\nfunction getFileExtension(filename) {\n  var _filename$split$pop;\n  return ((_filename$split$pop = filename.split('.').pop()) === null || _filename$split$pop === void 0 ? void 0 : _filename$split$pop.toLowerCase()) || 'docx';\n}\n\n// 生成唯一的文档密钥\nexport const generateDocumentKey = () => {\n  return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n};\n\n// 检查OnlyOffice服务是否可用\nexport const checkOnlyOfficeAvailability = async () => {\n  try {\n    // 检查API脚本是否可访问（这是更准确的检查方式）\n    const response = await fetch(ONLYOFFICE_CONFIG.apiUrl, {\n      method: 'HEAD',\n      mode: 'no-cors' // 避免CORS问题\n    });\n    return true; // 如果没有抛出错误，说明服务可用\n  } catch (error) {\n    console.error('OnlyOffice服务不可用:', error);\n    return false;\n  }\n};\n\n// 检查OnlyOffice服务器根路径是否可访问\nexport const checkOnlyOfficeServerRoot = async () => {\n  try {\n    const response = await fetch(`${ONLYOFFICE_CONFIG.documentServerUrl}/`, {\n      method: 'HEAD',\n      mode: 'no-cors'\n    });\n    return true;\n  } catch (error) {\n    console.error('OnlyOffice服务器根路径不可用:', error);\n    return false;\n  }\n};", "map": {"version": 3, "names": ["ONLYOFFICE_CONFIG", "documentServerUrl", "apiUrl", "backendUrl", "documentStorage", "baseUrl", "callbackUrl", "defaultDocument", "fileType", "title", "permissions", "edit", "download", "print", "review", "comment", "editorConfig", "mode", "lang", "user", "id", "name", "customization", "autosave", "chat", "comments", "help", "hideRightMenu", "hideRulers", "toolbar", "zoom", "plugins", "autostart", "pluginsData", "width", "height", "generateDocumentConfig", "documentUrl", "documentKey", "documentType", "getDocumentType", "document", "getFileExtension", "key", "url", "events", "onDocumentReady", "console", "log", "onDocumentStateChange", "event", "onError", "error", "filename", "ext", "includes", "_filename$split$pop", "split", "pop", "toLowerCase", "generateDocumentKey", "Date", "now", "Math", "random", "toString", "substr", "checkOnlyOfficeAvailability", "response", "fetch", "method", "checkOnlyOfficeServerRoot"], "sources": ["E:/k/cowriter/cowriter-frontend/src/config/onlyoffice.ts"], "sourcesContent": ["// OnlyOffice配置文件\nexport const ONLYOFFICE_CONFIG = {\n  // OnlyOffice Document Server的URL\n  documentServerUrl: 'http://localhost:8080',\n  \n  // API脚本URL\n  apiUrl: 'http://localhost:8080/web-apps/apps/api/documents/api.js',\n  \n  // 后端服务配置\n  backendUrl: 'http://localhost:3001',\n\n  // 文档存储配置\n  documentStorage: {\n    // 文档存储的基础URL（使用后端服务）\n    baseUrl: 'http://localhost:3001/files',\n\n    // 回调URL（OnlyOffice保存文档时的回调）\n    callbackUrl: 'http://localhost:3001/callback',\n  },\n  \n  // 默认文档配置\n  defaultDocument: {\n    fileType: 'docx',\n    title: '新建文档.docx',\n    permissions: {\n      edit: true,\n      download: true,\n      print: true,\n      review: true,\n      comment: true,\n    },\n  },\n  \n  // 编辑器配置\n  editorConfig: {\n    mode: 'edit' as const,\n    lang: 'zh-C<PERSON>',\n    user: {\n      id: 'user-1',\n      name: '用户',\n    },\n    customization: {\n      autosave: true,\n      chat: false,\n      comments: true,\n      help: true,\n      hideRightMenu: false,\n      hideRulers: false,\n      toolbar: true,\n      zoom: 100,\n      // 启用插件支持\n      plugins: true,\n    },\n    // 插件配置 - 添加公文排版插件和模板格式化插件\n    plugins: {\n      autostart: [\n        'asc.{OFFICIAL-DOC-FORMATTER-12345678-1234-5678-9ABC-123456789ABC}',\n        'asc.{TEMPLATE-FORMATTER-OFFICIAL-12345678-1234-5678-9ABC-123456789ABC}'\n      ],\n      pluginsData: [\n        'http://localhost:8080/sdkjs-plugins/asc.{OFFICIAL-DOC-FORMATTER-12345678-1234-5678-9ABC-123456789ABC}/config.json',\n        'http://localhost:8080/sdkjs-plugins/asc.{TEMPLATE-FORMATTER-OFFICIAL-12345678-1234-5678-9ABC-123456789ABC}/config.json'\n      ]\n    },\n  },\n\n  // 编辑器尺寸配置\n  width: '100%',\n  height: '100%',\n};\n\n// 生成文档配置的辅助函数\nexport const generateDocumentConfig = (documentUrl: string, documentKey: string, title: string) => {\n  return {\n    documentType: getDocumentType(title),\n    document: {\n      fileType: getFileExtension(title),\n      key: documentKey,\n      title: title,\n      url: documentUrl,\n      permissions: ONLYOFFICE_CONFIG.defaultDocument.permissions,\n    },\n    editorConfig: {\n      ...ONLYOFFICE_CONFIG.editorConfig,\n      callbackUrl: ONLYOFFICE_CONFIG.documentStorage.callbackUrl,\n    },\n    width: ONLYOFFICE_CONFIG.width,\n    height: ONLYOFFICE_CONFIG.height,\n    events: {\n      onDocumentReady: () => {\n        console.log('OnlyOffice文档已准备就绪');\n      },\n      onDocumentStateChange: (event: any) => {\n        console.log('文档状态变化:', event);\n      },\n      onError: (event: any) => {\n        console.error('OnlyOffice错误:', event);\n      },\n    },\n  };\n};\n\n// 根据文件名获取文档类型\nfunction getDocumentType(filename: string): 'word' | 'cell' | 'slide' {\n  const ext = getFileExtension(filename);\n  \n  if (['doc', 'docx', 'odt', 'rtf', 'txt'].includes(ext)) {\n    return 'word';\n  } else if (['xls', 'xlsx', 'ods', 'csv'].includes(ext)) {\n    return 'cell';\n  } else if (['ppt', 'pptx', 'odp'].includes(ext)) {\n    return 'slide';\n  }\n  \n  return 'word'; // 默认为word文档\n}\n\n// 获取文件扩展名\nfunction getFileExtension(filename: string): string {\n  return filename.split('.').pop()?.toLowerCase() || 'docx';\n}\n\n// 生成唯一的文档密钥\nexport const generateDocumentKey = (): string => {\n  return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n};\n\n// 检查OnlyOffice服务是否可用\nexport const checkOnlyOfficeAvailability = async (): Promise<boolean> => {\n  try {\n    // 检查API脚本是否可访问（这是更准确的检查方式）\n    const response = await fetch(ONLYOFFICE_CONFIG.apiUrl, {\n      method: 'HEAD',\n      mode: 'no-cors' // 避免CORS问题\n    });\n    return true; // 如果没有抛出错误，说明服务可用\n  } catch (error) {\n    console.error('OnlyOffice服务不可用:', error);\n    return false;\n  }\n};\n\n// 检查OnlyOffice服务器根路径是否可访问\nexport const checkOnlyOfficeServerRoot = async (): Promise<boolean> => {\n  try {\n    const response = await fetch(`${ONLYOFFICE_CONFIG.documentServerUrl}/`, {\n      method: 'HEAD',\n      mode: 'no-cors'\n    });\n    return true;\n  } catch (error) {\n    console.error('OnlyOffice服务器根路径不可用:', error);\n    return false;\n  }\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,iBAAiB,GAAG;EAC/B;EACAC,iBAAiB,EAAE,uBAAuB;EAE1C;EACAC,MAAM,EAAE,0DAA0D;EAElE;EACAC,UAAU,EAAE,uBAAuB;EAEnC;EACAC,eAAe,EAAE;IACf;IACAC,OAAO,EAAE,6BAA6B;IAEtC;IACAC,WAAW,EAAE;EACf,CAAC;EAED;EACAC,eAAe,EAAE;IACfC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE;MACXC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EAED;EACAC,YAAY,EAAE;IACZC,IAAI,EAAE,MAAe;IACrBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE;MACJC,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE;IACR,CAAC;IACDC,aAAa,EAAE;MACbC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,IAAI;MACVC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE,GAAG;MACT;MACAC,OAAO,EAAE;IACX,CAAC;IACD;IACAA,OAAO,EAAE;MACPC,SAAS,EAAE,CACT,mEAAmE,EACnE,wEAAwE,CACzE;MACDC,WAAW,EAAE,CACX,mHAAmH,EACnH,wHAAwH;IAE5H;EACF,CAAC;EAED;EACAC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE;AACV,CAAC;;AAED;AACA,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,WAAmB,EAAEC,WAAmB,EAAE7B,KAAa,KAAK;EACjG,OAAO;IACL8B,YAAY,EAAEC,eAAe,CAAC/B,KAAK,CAAC;IACpCgC,QAAQ,EAAE;MACRjC,QAAQ,EAAEkC,gBAAgB,CAACjC,KAAK,CAAC;MACjCkC,GAAG,EAAEL,WAAW;MAChB7B,KAAK,EAAEA,KAAK;MACZmC,GAAG,EAAEP,WAAW;MAChB3B,WAAW,EAAEV,iBAAiB,CAACO,eAAe,CAACG;IACjD,CAAC;IACDM,YAAY,EAAE;MACZ,GAAGhB,iBAAiB,CAACgB,YAAY;MACjCV,WAAW,EAAEN,iBAAiB,CAACI,eAAe,CAACE;IACjD,CAAC;IACD4B,KAAK,EAAElC,iBAAiB,CAACkC,KAAK;IAC9BC,MAAM,EAAEnC,iBAAiB,CAACmC,MAAM;IAChCU,MAAM,EAAE;MACNC,eAAe,EAAEA,CAAA,KAAM;QACrBC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAClC,CAAC;MACDC,qBAAqB,EAAGC,KAAU,IAAK;QACrCH,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEE,KAAK,CAAC;MAC/B,CAAC;MACDC,OAAO,EAAGD,KAAU,IAAK;QACvBH,OAAO,CAACK,KAAK,CAAC,eAAe,EAAEF,KAAK,CAAC;MACvC;IACF;EACF,CAAC;AACH,CAAC;;AAED;AACA,SAASV,eAAeA,CAACa,QAAgB,EAA6B;EACpE,MAAMC,GAAG,GAAGZ,gBAAgB,CAACW,QAAQ,CAAC;EAEtC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACE,QAAQ,CAACD,GAAG,CAAC,EAAE;IACtD,OAAO,MAAM;EACf,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,EAAE;IACtD,OAAO,MAAM;EACf,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,EAAE;IAC/C,OAAO,OAAO;EAChB;EAEA,OAAO,MAAM,CAAC,CAAC;AACjB;;AAEA;AACA,SAASZ,gBAAgBA,CAACW,QAAgB,EAAU;EAAA,IAAAG,mBAAA;EAClD,OAAO,EAAAA,mBAAA,GAAAH,QAAQ,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,cAAAF,mBAAA,uBAAzBA,mBAAA,CAA2BG,WAAW,CAAC,CAAC,KAAI,MAAM;AAC3D;;AAEA;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAAA,KAAc;EAC/C,OAAO,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;AACvE,CAAC;;AAED;AACA,OAAO,MAAMC,2BAA2B,GAAG,MAAAA,CAAA,KAA8B;EACvE,IAAI;IACF;IACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACrE,iBAAiB,CAACE,MAAM,EAAE;MACrDoE,MAAM,EAAE,MAAM;MACdrD,IAAI,EAAE,SAAS,CAAC;IAClB,CAAC,CAAC;IACF,OAAO,IAAI,CAAC,CAAC;EACf,CAAC,CAAC,OAAOmC,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IACxC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,OAAO,MAAMmB,yBAAyB,GAAG,MAAAA,CAAA,KAA8B;EACrE,IAAI;IACF,MAAMH,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGrE,iBAAiB,CAACC,iBAAiB,GAAG,EAAE;MACtEqE,MAAM,EAAE,MAAM;MACdrD,IAAI,EAAE;IACR,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC,CAAC,OAAOmC,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5C,OAAO,KAAK;EACd;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}