{"ast": null, "code": "var _jsxFileName = \"E:\\\\k\\\\cowriter\\\\cowriter-frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline, Box, Typography } from '@mui/material';\nimport Sidebar from './components/Sidebar';\nimport DocumentEditor from './components/DocumentEditor';\nimport AIChat from './components/AIChat';\nimport ProjectChat from './components/ProjectChat';\nimport GeneralChat from './components/GeneralChat';\nimport SettingsPage from './components/Settings';\nimport DifyTest from './components/DifyTest';\nimport WorkflowConfig from './components/WorkflowConfig';\nimport KnowledgeManager from './components/KnowledgeManager';\n// PDF处理现在在后端进行，不再需要前端导入\nimport { getBackendUrl } from './config/api';\nimport './App.css';\n\n// 🔑 接口定义\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// 创建主题 - 根据UI设计优化\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#6366f1',\n      // 紫蓝色主色调\n      light: '#a5b4fc',\n      dark: '#4338ca'\n    },\n    secondary: {\n      main: '#06b6d4',\n      // 青色用于辅助\n      light: '#67e8f9',\n      dark: '#0891b2'\n    },\n    background: {\n      default: '#fafbfc',\n      // 非常浅的灰白色背景\n      paper: '#ffffff'\n    },\n    text: {\n      primary: '#374151',\n      // 深灰色文字\n      secondary: '#6b7280' // 中灰色文字\n    },\n    grey: {\n      50: '#f9fafb',\n      100: '#f3f4f6',\n      200: '#e5e7eb',\n      300: '#d1d5db',\n      400: '#9ca3af',\n      500: '#6b7280',\n      600: '#4b5563',\n      700: '#374151',\n      800: '#1f2937',\n      900: '#111827'\n    }\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontSize: '2.25rem',\n      fontWeight: 700,\n      lineHeight: 1.2\n    },\n    h2: {\n      fontSize: '1.875rem',\n      fontWeight: 600,\n      lineHeight: 1.3\n    },\n    h3: {\n      fontSize: '1.5rem',\n      fontWeight: 600,\n      lineHeight: 1.4\n    },\n    h4: {\n      fontSize: '1.25rem',\n      fontWeight: 600,\n      lineHeight: 1.4\n    },\n    h5: {\n      fontSize: '1.125rem',\n      fontWeight: 600,\n      lineHeight: 1.4\n    },\n    h6: {\n      fontSize: '1rem',\n      fontWeight: 600,\n      lineHeight: 1.4\n    },\n    body1: {\n      fontSize: '1rem',\n      lineHeight: 1.6\n    },\n    body2: {\n      fontSize: '0.875rem',\n      lineHeight: 1.5\n    },\n    caption: {\n      fontSize: '0.75rem',\n      lineHeight: 1.4\n    }\n  },\n  shape: {\n    borderRadius: 8\n  },\n  shadows: ['none', '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)', '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)', '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)', '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)', '0 25px 50px -12px rgba(0, 0, 0, 0.25)', ...Array(19).fill('none')],\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 500,\n          borderRadius: 6,\n          padding: '8px 16px'\n        },\n        contained: {\n          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n          '&:hover': {\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'\n          }\n        }\n      }\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          backgroundImage: 'none'\n        }\n      }\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 6\n          }\n        }\n      }\n    },\n    MuiCssBaseline: {\n      styleOverrides: {\n        // 全局隐藏滚动条但保持滚动功能\n        '*::-webkit-scrollbar': {\n          width: '0px',\n          background: 'transparent'\n        },\n        '*::-webkit-scrollbar-track': {\n          background: 'transparent'\n        },\n        '*::-webkit-scrollbar-thumb': {\n          background: 'transparent'\n        },\n        // Firefox 隐藏滚动条\n        '*': {\n          scrollbarWidth: 'none'\n        },\n        // 确保body和html的滚动条也隐藏\n        'html, body': {\n          scrollbarWidth: 'none',\n          overflow: 'auto' // 保持滚动功能\n        }\n      }\n    }\n  }\n});\nfunction App() {\n  _s();\n  const [currentSection, setCurrentSection] = useState('pdf-chat');\n  const [isDocumentOpen, setIsDocumentOpen] = useState(false);\n  const [uploadedDocuments, setUploadedDocuments] = useState([]);\n  const [selectedDocument, setSelectedDocument] = useState(null);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [aiChatWidth, setAiChatWidth] = useState(400);\n  const [aiChatVisible, setAiChatVisible] = useState(true); // AI聊天面板显示状态\n  const [insertTextFunction, setInsertTextFunction] = useState(null); // 插入文本函数\n\n  // 包装插入文本函数，添加调试信息\n  const wrappedSetInsertTextFunction = fn => {\n    console.log('🔍 App: setInsertTextFunction被调用');\n    console.log('🔍 App: 函数类型:', typeof fn);\n    console.log('🔍 App: 函数存在:', !!fn);\n    setInsertTextFunction(fn);\n\n    // 立即验证状态是否正确设置\n    setTimeout(() => {\n      console.log('🔍 App: 验证insertTextFunction状态:', {\n        exists: !!insertTextFunction,\n        type: typeof insertTextFunction\n      });\n    }, 100);\n  };\n  const handleNavigationChange = section => {\n    // 如果切换到不同的主要section，清除相关状态\n    if (section === 'pdf-chat' && currentSection !== 'pdf-chat') {\n      // 切换到文件解读模式，清除文档状态，显示上传页面\n      setSelectedDocument(null);\n      setIsDocumentOpen(false);\n      setAiChatVisible(true); // 重置AI聊天面板为显示状态\n      setInsertTextFunction(null); // 清除插入函数\n    } else if (section === 'project-chat' && currentSection !== 'project-chat' && currentSection !== 'project-chat-editor') {\n      // 切换到公文生成模式，清除文档状态，显示创建页面\n      setSelectedDocument(null);\n      setIsDocumentOpen(false);\n      setAiChatVisible(true); // 重置AI聊天面板为显示状态\n      setInsertTextFunction(null); // 清除插入函数\n    } else if (section === 'general-chat' && currentSection !== 'general-chat') {\n      // 切换到通用聊天模式，清除文档状态\n      setSelectedDocument(null);\n      setIsDocumentOpen(false);\n      setAiChatVisible(true); // 重置AI聊天面板为显示状态\n      setInsertTextFunction(null); // 清除插入函数\n    } else if (section === 'settings' && currentSection !== 'settings') {\n      // 切换到设置页面，清除文档状态\n      setSelectedDocument(null);\n      setIsDocumentOpen(false);\n      setAiChatVisible(true); // 重置AI聊天面板为显示状态\n      setInsertTextFunction(null); // 清除插入函数\n    }\n    setCurrentSection(section);\n  };\n  const handleDocumentStateChange = isOpen => {\n    setIsDocumentOpen(isOpen);\n  };\n  const handleDocumentUpload = document => {\n    console.log('🔍 App: handleDocumentUpload 被调用，新文档:', document); // 调试日志\n\n    // 添加到文档列表\n    setUploadedDocuments(prev => [document, ...prev]);\n\n    // 🔑 关键：自动选择并打开新上传的文档\n    setSelectedDocument(document);\n    setIsDocumentOpen(true);\n    setCurrentSection('pdf-chat');\n    console.log('🔍 App: 新上传的文档已自动打开'); // 调试日志\n  };\n  const handleDocumentSelect = document => {\n    setSelectedDocument(document);\n    setIsDocumentOpen(true);\n  };\n  const handleDocumentDelete = async documentId => {\n    try {\n      // 调用后端API删除文档\n      const response = await fetch(getBackendUrl(`/files/${documentId}`), {\n        method: 'DELETE'\n      });\n      if (response.ok) {\n        // 从本地状态中移除文档\n        setUploadedDocuments(prev => prev.filter(doc => doc.id !== documentId));\n\n        // 如果删除的是当前选中的文档，关闭编辑器\n        if ((selectedDocument === null || selectedDocument === void 0 ? void 0 : selectedDocument.id) === documentId) {\n          setSelectedDocument(null);\n          setIsDocumentOpen(false);\n        }\n        console.log('文档删除成功');\n      } else {\n        console.error('删除文档失败:', response.statusText);\n        alert('删除文档失败，请重试。');\n      }\n    } catch (error) {\n      console.error('删除文档时发生错误:', error);\n      alert('删除文档时发生错误，请重试。');\n    }\n  };\n  const handleToggleSidebar = () => {\n    setSidebarCollapsed(prev => !prev);\n  };\n\n  // 🔑 处理添加新文档 - 直接弹出文件选择对话框\n  const handleAddNewDocument = () => {\n    console.log('🔍 App: handleAddNewDocument 被调用 - 直接选择文件'); // 调试日志\n\n    // 创建一个隐藏的文件输入元素\n    const fileInput = document.createElement('input');\n    fileInput.type = 'file';\n    fileInput.accept = '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt,.rtf,.odt,.ods,.odp';\n    fileInput.style.display = 'none';\n\n    // 处理文件选择\n    fileInput.onchange = async event => {\n      var _target$files;\n      const target = event.target;\n      const file = (_target$files = target.files) === null || _target$files === void 0 ? void 0 : _target$files[0];\n      if (file) {\n        console.log('🔍 App: 选择了文件:', file.name); // 调试日志\n\n        try {\n          // 上传文件到本地后端（用于OnlyOffice编辑）\n          const formData = new FormData();\n          formData.append('file', file);\n          const response = await fetch(getBackendUrl('/upload'), {\n            method: 'POST',\n            body: formData\n          });\n          if (response.ok) {\n            const result = await response.json();\n            console.log('🔍 App: 本地文件上传成功:', result); // 调试日志\n\n            // 尝试提取文档内容\n            let documentContent = '';\n            try {\n              console.log('🔍 App: 尝试提取文档内容...');\n              console.log('🔍 App: 文件信息:', {\n                fileName: file.name,\n                fileType: file.type,\n                fileSize: file.size,\n                resultId: result.fileId || result.id,\n                fullResult: result\n              });\n\n              // 统一使用后端API获取内容（包括PDF和docx）\n              const contentUrl = getBackendUrl(`/content/${result.fileId || result.id}`);\n              console.log('🔍 App: 请求内容提取API:', contentUrl);\n              const contentResponse = await fetch(contentUrl);\n              console.log('🔍 App: 内容API响应状态:', contentResponse.status);\n              if (contentResponse.ok) {\n                documentContent = await contentResponse.text();\n                console.log('🔍 App: 从后端获取文档内容成功，长度:', documentContent.length);\n                console.log('🔍 App: 内容预览:', documentContent.substring(0, 200) + '...');\n              } else {\n                const errorText = await contentResponse.text();\n                console.warn('🔍 App: 后端内容获取失败，状态:', contentResponse.status, '错误:', errorText);\n              }\n            } catch (error) {\n              console.error('🔍 App: 获取文档内容异常:', error);\n            }\n\n            // 创建文档对象\n            const newDocument = {\n              id: result.fileId || result.id,\n              // 🔑 兼容不同的返回格式\n              title: file.name,\n              // 🔑 优先使用前端原始文件名，避免后端编码问题\n              type: file.type || 'application/octet-stream',\n              size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,\n              uploadDate: new Date(),\n              description: `上传于 ${new Date().toLocaleString()}`,\n              content: documentContent // 🔑 添加文档内容\n            };\n\n            // 添加到文档列表\n            setUploadedDocuments(prev => [...prev, newDocument]);\n\n            // 直接选择并打开新文档\n            setSelectedDocument(newDocument);\n            setIsDocumentOpen(true);\n            setCurrentSection('pdf-chat');\n            console.log('🔍 App: 新文档已添加并打开'); // 调试日志\n          } else {\n            console.error('本地文件上传失败:', response.statusText);\n            alert('文件上传失败，请重试。');\n          }\n        } catch (error) {\n          console.error('上传文件时发生错误:', error);\n          alert('上传文件时发生错误，请重试。');\n        }\n      }\n\n      // 清理临时元素\n      document.body.removeChild(fileInput);\n    };\n\n    // 添加到DOM并触发点击\n    document.body.appendChild(fileInput);\n    fileInput.click();\n  };\n\n  // 🔑 新建空白文档功能\n  const handleCreateNewDocument = async () => {\n    console.log('🔍 App: handleCreateNewDocument 被调用 - 创建空白文档'); // 调试日志\n\n    try {\n      // 创建空白docx文档\n      const response = await fetch(getBackendUrl('/create-document'), {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          type: 'docx',\n          // 改为docx格式\n          title: `新建文档_${new Date().toLocaleDateString()}_${new Date().toLocaleTimeString().replace(/:/g, '-')}`\n        })\n      });\n      if (response.ok) {\n        const result = await response.json();\n        console.log('🔍 App: 新建文档响应:', result); // 调试日志\n\n        // 构建新文档对象\n        const newDocument = {\n          id: result.id || Date.now().toString(),\n          title: result.title || `新建文档_${new Date().toLocaleDateString()}`,\n          type: 'docx',\n          size: result.size ? `${(result.size / 1024 / 1024).toFixed(2)} MB` : '0.01 MB',\n          uploadDate: new Date(),\n          description: `创建于 ${new Date().toLocaleString()}`,\n          source: 'project-chat',\n          content: result.content || ''\n        };\n\n        // 添加到文档列表\n        setUploadedDocuments(prev => [newDocument, ...prev]);\n\n        // 🔑 关键：自动选择并打开新创建的文档\n        setSelectedDocument(newDocument);\n        setIsDocumentOpen(true);\n        setAiChatVisible(true); // 确保AI聊天面板显示\n        setCurrentSection('project-chat-editor'); // 切换到编辑器状态\n\n        console.log('🔍 App: 新建文档已添加并打开'); // 调试日志\n      } else {\n        console.error('创建文档失败:', response.statusText);\n        alert('创建文档失败，请重试。');\n      }\n    } catch (error) {\n      console.error('创建文档失败:', error);\n      alert('创建文档失败，请重试');\n    }\n  };\n\n  // 🔑 基于公文模板新建文档功能\n  const handleCreateTemplateDocument = async () => {\n    console.log('🔍 App: handleCreateTemplateDocument 被调用 - 基于公文模板新建'); // 调试日志\n\n    try {\n      // 创建基于模板的docx文档\n      const response = await fetch(getBackendUrl('/create-document'), {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          type: 'docx',\n          template: 'official',\n          // 标识使用公文模板\n          title: `公文_${new Date().toLocaleDateString()}_${new Date().toLocaleTimeString().replace(/:/g, '-')}`\n        })\n      });\n      if (response.ok) {\n        const result = await response.json();\n        console.log('🔍 App: 基于模板新建文档响应:', result); // 调试日志\n\n        // 构建新文档对象\n        const newDocument = {\n          id: result.id || Date.now().toString(),\n          title: result.title || `公文_${new Date().toLocaleDateString()}`,\n          type: 'docx',\n          size: result.size ? `${(result.size / 1024 / 1024).toFixed(2)} MB` : '0.01 MB',\n          uploadDate: new Date(),\n          description: `基于模板创建于 ${new Date().toLocaleString()}`,\n          source: 'project-chat',\n          content: result.content || ''\n        };\n\n        // 添加到文档列表\n        setUploadedDocuments(prev => [newDocument, ...prev]);\n\n        // 🔑 关键：自动选择并打开新创建的文档\n        setSelectedDocument(newDocument);\n        setIsDocumentOpen(true);\n        setAiChatVisible(true); // 确保AI聊天面板显示\n        setCurrentSection('project-chat-editor'); // 切换到编辑器状态\n\n        console.log('🔍 App: 基于模板新建文档已添加并打开'); // 调试日志\n\n        // 发送消息给OnlyOffice，通知应用模板\n        setTimeout(() => {\n          const editorFrame = document.querySelector('#onlyoffice-editor iframe');\n          if (editorFrame && editorFrame.contentWindow) {\n            const message = {\n              type: 'applyOfficialTemplate',\n              template: 'official'\n            };\n            editorFrame.contentWindow.postMessage(message, '*');\n            console.log('🔍 App: 已发送模板应用消息给OnlyOffice');\n          }\n        }, 2000); // 等待2秒确保OnlyOffice加载完成\n      } else {\n        console.error('基于模板创建文档失败:', response.statusText);\n        alert('基于模板创建文档失败，请重试。');\n      }\n    } catch (error) {\n      console.error('基于模板创建文档失败:', error);\n      alert('基于模板创建文档失败，请重试');\n    }\n  };\n\n  // 🔑 处理通用聊天开始\n  const handleStartGeneralChat = (message, mode) => {\n    console.log('开始通用聊天:', {\n      message,\n      mode\n    });\n    // TODO: 这里可以实现聊天逻辑，比如跳转到聊天界面或发送消息\n    // 暂时只是打印日志，后续可以扩展\n  };\n\n  // 🔑 处理系统提示词变更\n  const handlePromptChange = prompt => {\n    console.log('系统提示词已更新:', prompt);\n    // TODO: 这里可以实现提示词变更的逻辑，比如更新全局状态或通知其他组件\n    // 暂时只是打印日志，后续可以扩展\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"main-grid\",\n      sx: {\n        display: 'grid',\n        gridTemplateColumns:\n        // 公文生成模式：只有侧边栏和主内容区\n        currentSection.startsWith('project-chat') ? `${sidebarCollapsed ? '60px' : '180px'} 1fr`\n        // 其他模式：根据AI聊天边栏状态决定\n        : isDocumentOpen && aiChatVisible ? `${sidebarCollapsed ? '60px' : '180px'} 1fr ${aiChatWidth}px` : `${sidebarCollapsed ? '60px' : '180px'} 1fr`,\n        height: '100vh',\n        width: '100vw',\n        maxWidth: '100vw',\n        overflow: 'hidden',\n        backgroundColor: 'background.default'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        onNavigationChange: handleNavigationChange,\n        uploadedDocuments: uploadedDocuments,\n        onDocumentSelect: handleDocumentSelect,\n        onDocumentDelete: handleDocumentDelete,\n        onAddNewDocument: handleAddNewDocument // 🔑 传递添加新文档的回调\n        ,\n        collapsed: sidebarCollapsed,\n        onToggleCollapse: handleToggleSidebar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          backgroundColor: '#f8fafc',\n          display: 'flex',\n          flexDirection: 'column',\n          height: '100%',\n          // 公文生成模式下填满剩余空间，其他模式保持原有逻辑\n          width: currentSection.startsWith('project-chat') ? '100%' : '100%',\n          maxWidth: currentSection.startsWith('project-chat') ? '100%' : '100%',\n          overflow: 'hidden',\n          paddingRight: isDocumentOpen && !currentSection.startsWith('project-chat') ? 0 : 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: '100%',\n            height: '100%',\n            maxWidth: '100%',\n            backgroundColor: 'white',\n            overflow: currentSection === 'settings' ? 'auto' : 'hidden',\n            display: 'flex',\n            flexDirection: 'column',\n            // 文档打开时：无圆角，填满整个容器\n            // 文档未打开时：有圆角和阴影，带边距\n            // 公文生成模式：始终填满容器\n            ...(isDocumentOpen || currentSection.startsWith('project-chat') ? {\n              borderRadius: 0,\n              margin: 0,\n              boxShadow: 'none'\n            } : {\n              borderRadius: 3,\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n              marginTop: 2,\n              marginRight: 2,\n              marginBottom: 2,\n              marginLeft: 2,\n              // 恢复左侧边距\n              minHeight: 'calc(100vh - 32px)',\n              // 确保容器不会溢出，减去左右边距\n              width: 'calc(100% - 16px)',\n              // 100% - (marginLeft + marginRight) * 8px\n              maxWidth: 'calc(100% - 16px)'\n            })\n          },\n          children: currentSection === 'project-chat' ? /*#__PURE__*/_jsxDEV(ProjectChat, {\n            onCreateDocument: handleCreateNewDocument,\n            onCreateTemplateDocument: handleCreateTemplateDocument\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 15\n          }, this) : currentSection === 'project-chat-editor' ? /*#__PURE__*/_jsxDEV(DocumentEditor, {\n            currentSection: \"project-chat\" // 传递project-chat给DocumentEditor\n            ,\n            onDocumentStateChange: handleDocumentStateChange,\n            onDocumentUpload: handleDocumentUpload,\n            selectedDocumentFromSidebar: selectedDocument,\n            onEditorReady: wrappedSetInsertTextFunction // 传递编辑器就绪回调\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 15\n          }, this) : currentSection === 'pdf-chat' ? /*#__PURE__*/_jsxDEV(DocumentEditor, {\n            currentSection: currentSection,\n            onDocumentStateChange: handleDocumentStateChange,\n            onDocumentUpload: handleDocumentUpload,\n            selectedDocumentFromSidebar: selectedDocument,\n            onEditorReady: wrappedSetInsertTextFunction // 传递编辑器就绪回调\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 15\n          }, this) : currentSection === 'general-chat' ? /*#__PURE__*/_jsxDEV(GeneralChat, {\n            onStartChat: handleStartGeneralChat\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 15\n          }, this) : currentSection === 'knowledge-manager' ? /*#__PURE__*/_jsxDEV(KnowledgeManager, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 15\n          }, this) : currentSection === 'settings' ? /*#__PURE__*/_jsxDEV(SettingsPage, {\n            onPromptChange: handlePromptChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 15\n          }, this) : currentSection === 'dify-test' ? /*#__PURE__*/_jsxDEV(DifyTest, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 15\n          }, this) : currentSection === 'workflow-config' ? /*#__PURE__*/_jsxDEV(WorkflowConfig, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(DocumentEditor, {\n            currentSection: currentSection,\n            onDocumentStateChange: handleDocumentStateChange,\n            onDocumentUpload: handleDocumentUpload,\n            selectedDocumentFromSidebar: selectedDocument,\n            onEditorReady: wrappedSetInsertTextFunction // 传递编辑器就绪回调\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 9\n      }, this), isDocumentOpen && aiChatVisible && !currentSection.startsWith('project-chat') && /*#__PURE__*/_jsxDEV(AIChat, {\n        currentSection: currentSection,\n        width: aiChatWidth,\n        onWidthChange: setAiChatWidth,\n        selectedDocument: selectedDocument,\n        onToggleVisibility: () => setAiChatVisible(!aiChatVisible),\n        insertTextFunction: insertTextFunction // 传递插入文本函数\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 11\n      }, this), isDocumentOpen && !aiChatVisible && !currentSection.startsWith('project-chat') && /*#__PURE__*/_jsxDEV(Box, {\n        onClick: () => setAiChatVisible(true),\n        sx: {\n          position: 'fixed',\n          right: 0,\n          top: '50%',\n          transform: 'translateY(-50%)',\n          width: '40px',\n          height: '100px',\n          backgroundColor: '#f3f4f6',\n          border: '1px solid #e5e7eb',\n          borderRight: 'none',\n          borderRadius: '8px 0 0 8px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          cursor: 'pointer',\n          zIndex: 1000,\n          '&:hover': {\n            backgroundColor: '#e5e7eb'\n          },\n          transition: 'background-color 0.2s ease'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            writingMode: 'vertical-rl',\n            textOrientation: 'mixed',\n            fontSize: '0.875rem',\n            color: '#6b7280',\n            fontWeight: 500\n          },\n          children: \"AI\\u804A\\u5929\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 694,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 537,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"/ccXjsUWey87smXkiKW/gEAZf58=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "ThemeProvider", "createTheme", "CssBaseline", "Box", "Typography", "Sidebar", "DocumentEditor", "AIChat", "ProjectChat", "GeneralChat", "SettingsPage", "DifyTest", "WorkflowConfig", "KnowledgeManager", "getBackendUrl", "jsxDEV", "_jsxDEV", "theme", "palette", "mode", "primary", "main", "light", "dark", "secondary", "background", "default", "paper", "text", "grey", "typography", "fontFamily", "h1", "fontSize", "fontWeight", "lineHeight", "h2", "h3", "h4", "h5", "h6", "body1", "body2", "caption", "shape", "borderRadius", "shadows", "Array", "fill", "components", "MuiB<PERSON>on", "styleOverrides", "root", "textTransform", "padding", "contained", "boxShadow", "MuiPaper", "backgroundImage", "MuiTextField", "MuiCssBaseline", "width", "scrollbarWidth", "overflow", "App", "_s", "currentSection", "setCurrentSection", "isDocumentOpen", "setIsDocumentOpen", "uploadedDocuments", "setUploadedDocuments", "selectedDocument", "setSelectedDocument", "sidebarCollapsed", "setSidebarCollapsed", "aiChatWidth", "setAiChatWidth", "aiChatVisible", "setAiChatVisible", "insertTextFunction", "setInsertTextFunction", "wrappedSetInsertTextFunction", "fn", "console", "log", "setTimeout", "exists", "type", "handleNavigationChange", "section", "handleDocumentStateChange", "isOpen", "handleDocumentUpload", "document", "prev", "handleDocumentSelect", "handleDocumentDelete", "documentId", "response", "fetch", "method", "ok", "filter", "doc", "id", "error", "statusText", "alert", "handleToggleSidebar", "handleAddNewDocument", "fileInput", "createElement", "accept", "style", "display", "onchange", "event", "_target$files", "target", "file", "files", "name", "formData", "FormData", "append", "body", "result", "json", "documentContent", "fileName", "fileType", "fileSize", "size", "resultId", "fileId", "fullResult", "contentUrl", "contentResponse", "status", "length", "substring", "errorText", "warn", "newDocument", "title", "toFixed", "uploadDate", "Date", "description", "toLocaleString", "content", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "click", "handleCreateNewDocument", "headers", "JSON", "stringify", "toLocaleDateString", "toLocaleTimeString", "replace", "now", "toString", "source", "handleCreateTemplateDocument", "template", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "contentWindow", "message", "postMessage", "handleStartGeneralChat", "handlePromptChange", "prompt", "children", "_jsxFileName", "lineNumber", "columnNumber", "className", "sx", "gridTemplateColumns", "startsWith", "height", "max<PERSON><PERSON><PERSON>", "backgroundColor", "onNavigationChange", "onDocumentSelect", "onDocumentDelete", "onAddNewDocument", "collapsed", "onToggleCollapse", "flexDirection", "paddingRight", "margin", "marginTop", "marginRight", "marginBottom", "marginLeft", "minHeight", "onCreateDocument", "onCreateTemplateDocument", "onDocumentStateChange", "onDocumentUpload", "selectedDocumentFromSidebar", "onEditorReady", "onStartChat", "onPromptChange", "onWidthChange", "onToggleVisibility", "onClick", "position", "right", "top", "transform", "border", "borderRight", "alignItems", "justifyContent", "cursor", "zIndex", "transition", "writingMode", "textOrientation", "color", "_c", "$RefreshReg$"], "sources": ["E:/k/cowriter/cowriter-frontend/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline, Box, Typography } from '@mui/material';\nimport { Settings } from '@mui/icons-material';\nimport Sidebar from './components/Sidebar';\nimport DocumentEditor from './components/DocumentEditor';\nimport AIChat from './components/AIChat';\nimport ProjectChat from './components/ProjectChat';\nimport GeneralChat from './components/GeneralChat';\nimport SettingsPage from './components/Settings';\nimport DifyTest from './components/DifyTest';\nimport WorkflowConfig from './components/WorkflowConfig';\nimport KnowledgeManager from './components/KnowledgeManager';\nimport { difyService } from './services/difyService';\n// PDF处理现在在后端进行，不再需要前端导入\nimport { getBackendUrl } from './config/api';\nimport './App.css';\n\n// 🔑 接口定义\ninterface UploadedDocument {\n  id: string;\n  title: string;\n  type: string;\n  size: string;\n  uploadDate: Date;\n  description?: string;\n  source?: 'pdf-chat' | 'project-chat'; // 文档来源\n  content?: string; // 文档内容\n}\n\n// 创建主题 - 根据UI设计优化\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#6366f1', // 紫蓝色主色调\n      light: '#a5b4fc',\n      dark: '#4338ca',\n    },\n    secondary: {\n      main: '#06b6d4', // 青色用于辅助\n      light: '#67e8f9',\n      dark: '#0891b2',\n    },\n    background: {\n      default: '#fafbfc', // 非常浅的灰白色背景\n      paper: '#ffffff',\n    },\n    text: {\n      primary: '#374151', // 深灰色文字\n      secondary: '#6b7280', // 中灰色文字\n    },\n    grey: {\n      50: '#f9fafb',\n      100: '#f3f4f6',\n      200: '#e5e7eb',\n      300: '#d1d5db',\n      400: '#9ca3af',\n      500: '#6b7280',\n      600: '#4b5563',\n      700: '#374151',\n      800: '#1f2937',\n      900: '#111827',\n    },\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontSize: '2.25rem',\n      fontWeight: 700,\n      lineHeight: 1.2,\n    },\n    h2: {\n      fontSize: '1.875rem',\n      fontWeight: 600,\n      lineHeight: 1.3,\n    },\n    h3: {\n      fontSize: '1.5rem',\n      fontWeight: 600,\n      lineHeight: 1.4,\n    },\n    h4: {\n      fontSize: '1.25rem',\n      fontWeight: 600,\n      lineHeight: 1.4,\n    },\n    h5: {\n      fontSize: '1.125rem',\n      fontWeight: 600,\n      lineHeight: 1.4,\n    },\n    h6: {\n      fontSize: '1rem',\n      fontWeight: 600,\n      lineHeight: 1.4,\n    },\n    body1: {\n      fontSize: '1rem',\n      lineHeight: 1.6,\n    },\n    body2: {\n      fontSize: '0.875rem',\n      lineHeight: 1.5,\n    },\n    caption: {\n      fontSize: '0.75rem',\n      lineHeight: 1.4,\n    },\n  },\n  shape: {\n    borderRadius: 8,\n  },\n  shadows: [\n    'none',\n    '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n    '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n    '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n    '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',\n    '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n    ...Array(19).fill('none'),\n  ] as any,\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 500,\n          borderRadius: 6,\n          padding: '8px 16px',\n        },\n        contained: {\n          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',\n          '&:hover': {\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n          },\n        },\n      },\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          backgroundImage: 'none',\n        },\n      },\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 6,\n          },\n        },\n      },\n    },\n    MuiCssBaseline: {\n      styleOverrides: {\n        // 全局隐藏滚动条但保持滚动功能\n        '*::-webkit-scrollbar': {\n          width: '0px',\n          background: 'transparent',\n        },\n        '*::-webkit-scrollbar-track': {\n          background: 'transparent',\n        },\n        '*::-webkit-scrollbar-thumb': {\n          background: 'transparent',\n        },\n        // Firefox 隐藏滚动条\n        '*': {\n          scrollbarWidth: 'none',\n        },\n        // 确保body和html的滚动条也隐藏\n        'html, body': {\n          scrollbarWidth: 'none',\n          overflow: 'auto', // 保持滚动功能\n        },\n      },\n    },\n  },\n});\n\n\n\nfunction App() {\n  const [currentSection, setCurrentSection] = useState('pdf-chat');\n  const [isDocumentOpen, setIsDocumentOpen] = useState(false);\n  const [uploadedDocuments, setUploadedDocuments] = useState<UploadedDocument[]>([]);\n  const [selectedDocument, setSelectedDocument] = useState<UploadedDocument | null>(null);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [aiChatWidth, setAiChatWidth] = useState(400);\n  const [aiChatVisible, setAiChatVisible] = useState(true); // AI聊天面板显示状态\n  const [insertTextFunction, setInsertTextFunction] = useState<((text: string) => void) | null>(null); // 插入文本函数\n\n  // 包装插入文本函数，添加调试信息\n  const wrappedSetInsertTextFunction = (fn: ((text: string) => void) | null) => {\n    console.log('🔍 App: setInsertTextFunction被调用');\n    console.log('🔍 App: 函数类型:', typeof fn);\n    console.log('🔍 App: 函数存在:', !!fn);\n    setInsertTextFunction(fn);\n\n    // 立即验证状态是否正确设置\n    setTimeout(() => {\n      console.log('🔍 App: 验证insertTextFunction状态:', {\n        exists: !!insertTextFunction,\n        type: typeof insertTextFunction\n      });\n    }, 100);\n  };\n\n\n  const handleNavigationChange = (section: string) => {\n    // 如果切换到不同的主要section，清除相关状态\n    if (section === 'pdf-chat' && currentSection !== 'pdf-chat') {\n      // 切换到文件解读模式，清除文档状态，显示上传页面\n      setSelectedDocument(null);\n      setIsDocumentOpen(false);\n      setAiChatVisible(true); // 重置AI聊天面板为显示状态\n      setInsertTextFunction(null); // 清除插入函数\n    } else if (section === 'project-chat' && currentSection !== 'project-chat' && currentSection !== 'project-chat-editor') {\n      // 切换到公文生成模式，清除文档状态，显示创建页面\n      setSelectedDocument(null);\n      setIsDocumentOpen(false);\n      setAiChatVisible(true); // 重置AI聊天面板为显示状态\n      setInsertTextFunction(null); // 清除插入函数\n    } else if (section === 'general-chat' && currentSection !== 'general-chat') {\n      // 切换到通用聊天模式，清除文档状态\n      setSelectedDocument(null);\n      setIsDocumentOpen(false);\n      setAiChatVisible(true); // 重置AI聊天面板为显示状态\n      setInsertTextFunction(null); // 清除插入函数\n    } else if (section === 'settings' && currentSection !== 'settings') {\n      // 切换到设置页面，清除文档状态\n      setSelectedDocument(null);\n      setIsDocumentOpen(false);\n      setAiChatVisible(true); // 重置AI聊天面板为显示状态\n      setInsertTextFunction(null); // 清除插入函数\n    }\n\n    setCurrentSection(section);\n  };\n\n  const handleDocumentStateChange = (isOpen: boolean) => {\n    setIsDocumentOpen(isOpen);\n  };\n\n  const handleDocumentUpload = (document: UploadedDocument) => {\n    console.log('🔍 App: handleDocumentUpload 被调用，新文档:', document); // 调试日志\n\n    // 添加到文档列表\n    setUploadedDocuments(prev => [document, ...prev]);\n\n    // 🔑 关键：自动选择并打开新上传的文档\n    setSelectedDocument(document);\n    setIsDocumentOpen(true);\n    setCurrentSection('pdf-chat');\n\n    console.log('🔍 App: 新上传的文档已自动打开'); // 调试日志\n  };\n\n  const handleDocumentSelect = (document: UploadedDocument) => {\n    setSelectedDocument(document);\n    setIsDocumentOpen(true);\n  };\n\n  const handleDocumentDelete = async (documentId: string) => {\n    try {\n      // 调用后端API删除文档\n      const response = await fetch(getBackendUrl(`/files/${documentId}`), {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        // 从本地状态中移除文档\n        setUploadedDocuments(prev => prev.filter(doc => doc.id !== documentId));\n\n        // 如果删除的是当前选中的文档，关闭编辑器\n        if (selectedDocument?.id === documentId) {\n          setSelectedDocument(null);\n          setIsDocumentOpen(false);\n        }\n\n        console.log('文档删除成功');\n      } else {\n        console.error('删除文档失败:', response.statusText);\n        alert('删除文档失败，请重试。');\n      }\n    } catch (error) {\n      console.error('删除文档时发生错误:', error);\n      alert('删除文档时发生错误，请重试。');\n    }\n  };\n\n  const handleToggleSidebar = () => {\n    setSidebarCollapsed(prev => !prev);\n  };\n\n  // 🔑 处理添加新文档 - 直接弹出文件选择对话框\n  const handleAddNewDocument = () => {\n    console.log('🔍 App: handleAddNewDocument 被调用 - 直接选择文件'); // 调试日志\n\n    // 创建一个隐藏的文件输入元素\n    const fileInput = document.createElement('input');\n    fileInput.type = 'file';\n    fileInput.accept = '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt,.rtf,.odt,.ods,.odp';\n    fileInput.style.display = 'none';\n\n    // 处理文件选择\n    fileInput.onchange = async (event) => {\n      const target = event.target as HTMLInputElement;\n      const file = target.files?.[0];\n\n      if (file) {\n        console.log('🔍 App: 选择了文件:', file.name); // 调试日志\n\n        try {\n          // 上传文件到本地后端（用于OnlyOffice编辑）\n          const formData = new FormData();\n          formData.append('file', file);\n\n          const response = await fetch(getBackendUrl('/upload'), {\n            method: 'POST',\n            body: formData,\n          });\n\n          if (response.ok) {\n            const result = await response.json();\n            console.log('🔍 App: 本地文件上传成功:', result); // 调试日志\n\n            // 尝试提取文档内容\n            let documentContent = '';\n            try {\n              console.log('🔍 App: 尝试提取文档内容...');\n              console.log('🔍 App: 文件信息:', {\n                fileName: file.name,\n                fileType: file.type,\n                fileSize: file.size,\n                resultId: result.fileId || result.id,\n                fullResult: result\n              });\n\n              // 统一使用后端API获取内容（包括PDF和docx）\n              const contentUrl = getBackendUrl(`/content/${result.fileId || result.id}`);\n              console.log('🔍 App: 请求内容提取API:', contentUrl);\n\n              const contentResponse = await fetch(contentUrl);\n              console.log('🔍 App: 内容API响应状态:', contentResponse.status);\n\n              if (contentResponse.ok) {\n                documentContent = await contentResponse.text();\n                console.log('🔍 App: 从后端获取文档内容成功，长度:', documentContent.length);\n                console.log('🔍 App: 内容预览:', documentContent.substring(0, 200) + '...');\n              } else {\n                const errorText = await contentResponse.text();\n                console.warn('🔍 App: 后端内容获取失败，状态:', contentResponse.status, '错误:', errorText);\n              }\n            } catch (error) {\n              console.error('🔍 App: 获取文档内容异常:', error);\n            }\n\n            // 创建文档对象\n            const newDocument: UploadedDocument = {\n              id: result.fileId || result.id, // 🔑 兼容不同的返回格式\n              title: file.name, // 🔑 优先使用前端原始文件名，避免后端编码问题\n              type: file.type || 'application/octet-stream',\n              size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,\n              uploadDate: new Date(),\n              description: `上传于 ${new Date().toLocaleString()}`,\n              content: documentContent // 🔑 添加文档内容\n            };\n\n            // 添加到文档列表\n            setUploadedDocuments(prev => [...prev, newDocument]);\n\n            // 直接选择并打开新文档\n            setSelectedDocument(newDocument);\n            setIsDocumentOpen(true);\n            setCurrentSection('pdf-chat');\n\n            console.log('🔍 App: 新文档已添加并打开'); // 调试日志\n          } else {\n            console.error('本地文件上传失败:', response.statusText);\n            alert('文件上传失败，请重试。');\n          }\n        } catch (error) {\n          console.error('上传文件时发生错误:', error);\n          alert('上传文件时发生错误，请重试。');\n        }\n      }\n\n      // 清理临时元素\n      document.body.removeChild(fileInput);\n    };\n\n    // 添加到DOM并触发点击\n    document.body.appendChild(fileInput);\n    fileInput.click();\n  };\n\n\n\n  // 🔑 新建空白文档功能\n  const handleCreateNewDocument = async () => {\n    console.log('🔍 App: handleCreateNewDocument 被调用 - 创建空白文档'); // 调试日志\n\n    try {\n      // 创建空白docx文档\n      const response = await fetch(getBackendUrl('/create-document'), {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          type: 'docx', // 改为docx格式\n          title: `新建文档_${new Date().toLocaleDateString()}_${new Date().toLocaleTimeString().replace(/:/g, '-')}`\n        }),\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log('🔍 App: 新建文档响应:', result); // 调试日志\n\n        // 构建新文档对象\n        const newDocument: UploadedDocument = {\n          id: result.id || Date.now().toString(),\n          title: result.title || `新建文档_${new Date().toLocaleDateString()}`,\n          type: 'docx',\n          size: result.size ? `${(result.size / 1024 / 1024).toFixed(2)} MB` : '0.01 MB',\n          uploadDate: new Date(),\n          description: `创建于 ${new Date().toLocaleString()}`,\n          source: 'project-chat',\n          content: result.content || ''\n        };\n\n        // 添加到文档列表\n        setUploadedDocuments(prev => [newDocument, ...prev]);\n\n        // 🔑 关键：自动选择并打开新创建的文档\n        setSelectedDocument(newDocument);\n        setIsDocumentOpen(true);\n        setAiChatVisible(true); // 确保AI聊天面板显示\n        setCurrentSection('project-chat-editor'); // 切换到编辑器状态\n\n        console.log('🔍 App: 新建文档已添加并打开'); // 调试日志\n      } else {\n        console.error('创建文档失败:', response.statusText);\n        alert('创建文档失败，请重试。');\n      }\n    } catch (error) {\n      console.error('创建文档失败:', error);\n      alert('创建文档失败，请重试');\n    }\n  };\n\n  // 🔑 基于公文模板新建文档功能\n  const handleCreateTemplateDocument = async () => {\n    console.log('🔍 App: handleCreateTemplateDocument 被调用 - 基于公文模板新建'); // 调试日志\n\n    try {\n      // 创建基于模板的docx文档\n      const response = await fetch(getBackendUrl('/create-document'), {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          type: 'docx',\n          template: 'official', // 标识使用公文模板\n          title: `公文_${new Date().toLocaleDateString()}_${new Date().toLocaleTimeString().replace(/:/g, '-')}`\n        }),\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log('🔍 App: 基于模板新建文档响应:', result); // 调试日志\n\n        // 构建新文档对象\n        const newDocument: UploadedDocument = {\n          id: result.id || Date.now().toString(),\n          title: result.title || `公文_${new Date().toLocaleDateString()}`,\n          type: 'docx',\n          size: result.size ? `${(result.size / 1024 / 1024).toFixed(2)} MB` : '0.01 MB',\n          uploadDate: new Date(),\n          description: `基于模板创建于 ${new Date().toLocaleString()}`,\n          source: 'project-chat',\n          content: result.content || ''\n        };\n\n        // 添加到文档列表\n        setUploadedDocuments(prev => [newDocument, ...prev]);\n\n        // 🔑 关键：自动选择并打开新创建的文档\n        setSelectedDocument(newDocument);\n        setIsDocumentOpen(true);\n        setAiChatVisible(true); // 确保AI聊天面板显示\n        setCurrentSection('project-chat-editor'); // 切换到编辑器状态\n\n        console.log('🔍 App: 基于模板新建文档已添加并打开'); // 调试日志\n\n        // 发送消息给OnlyOffice，通知应用模板\n        setTimeout(() => {\n          const editorFrame = document.querySelector('#onlyoffice-editor iframe') as HTMLIFrameElement;\n          if (editorFrame && editorFrame.contentWindow) {\n            const message = {\n              type: 'applyOfficialTemplate',\n              template: 'official'\n            };\n            editorFrame.contentWindow.postMessage(message, '*');\n            console.log('🔍 App: 已发送模板应用消息给OnlyOffice');\n          }\n        }, 2000); // 等待2秒确保OnlyOffice加载完成\n      } else {\n        console.error('基于模板创建文档失败:', response.statusText);\n        alert('基于模板创建文档失败，请重试。');\n      }\n    } catch (error) {\n      console.error('基于模板创建文档失败:', error);\n      alert('基于模板创建文档失败，请重试');\n    }\n  };\n\n  // 🔑 处理通用聊天开始\n  const handleStartGeneralChat = (message: string, mode: string) => {\n    console.log('开始通用聊天:', { message, mode });\n    // TODO: 这里可以实现聊天逻辑，比如跳转到聊天界面或发送消息\n    // 暂时只是打印日志，后续可以扩展\n  };\n\n  // 🔑 处理系统提示词变更\n  const handlePromptChange = (prompt: any) => {\n    console.log('系统提示词已更新:', prompt);\n    // TODO: 这里可以实现提示词变更的逻辑，比如更新全局状态或通知其他组件\n    // 暂时只是打印日志，后续可以扩展\n  };\n\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Box\n        className=\"main-grid\"\n        sx={{\n          display: 'grid',\n          gridTemplateColumns:\n            // 公文生成模式：只有侧边栏和主内容区\n            currentSection.startsWith('project-chat')\n              ? `${sidebarCollapsed ? '60px' : '180px'} 1fr`\n              // 其他模式：根据AI聊天边栏状态决定\n              : (isDocumentOpen && aiChatVisible\n                ? `${sidebarCollapsed ? '60px' : '180px'} 1fr ${aiChatWidth}px`\n                : `${sidebarCollapsed ? '60px' : '180px'} 1fr`),\n          height: '100vh',\n          width: '100vw',\n          maxWidth: '100vw',\n          overflow: 'hidden',\n          backgroundColor: 'background.default',\n        }}\n      >\n        {/* 左侧边栏 */}\n        <Sidebar\n          onNavigationChange={handleNavigationChange}\n          uploadedDocuments={uploadedDocuments}\n          onDocumentSelect={handleDocumentSelect}\n          onDocumentDelete={handleDocumentDelete}\n          onAddNewDocument={handleAddNewDocument} // 🔑 传递添加新文档的回调\n          collapsed={sidebarCollapsed}\n          onToggleCollapse={handleToggleSidebar}\n        />\n\n        {/* 中间文档编辑器区域 - 带白色圆角底框 */}\n        <Box\n          sx={{\n            backgroundColor: '#f8fafc',\n            display: 'flex',\n            flexDirection: 'column',\n            height: '100%',\n            // 公文生成模式下填满剩余空间，其他模式保持原有逻辑\n            width: currentSection.startsWith('project-chat') ? '100%' : '100%',\n            maxWidth: currentSection.startsWith('project-chat') ? '100%' : '100%',\n            overflow: 'hidden',\n            paddingRight: (isDocumentOpen && !currentSection.startsWith('project-chat')) ? 0 : 1,\n          }}\n        >\n          <Box\n            sx={{\n              width: '100%',\n              height: '100%',\n              maxWidth: '100%',\n              backgroundColor: 'white',\n              overflow: currentSection === 'settings' ? 'auto' : 'hidden',\n              display: 'flex',\n              flexDirection: 'column',\n              // 文档打开时：无圆角，填满整个容器\n              // 文档未打开时：有圆角和阴影，带边距\n              // 公文生成模式：始终填满容器\n              ...(isDocumentOpen || currentSection.startsWith('project-chat') ? {\n                borderRadius: 0,\n                margin: 0,\n                boxShadow: 'none',\n              } : {\n                borderRadius: 3,\n                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n                marginTop: 2,\n                marginRight: 2,\n                marginBottom: 2,\n                marginLeft: 2,  // 恢复左侧边距\n                minHeight: 'calc(100vh - 32px)',\n                // 确保容器不会溢出，减去左右边距\n                width: 'calc(100% - 16px)', // 100% - (marginLeft + marginRight) * 8px\n                maxWidth: 'calc(100% - 16px)',\n              }),\n            }}\n          >\n            {currentSection === 'project-chat' ? (\n              <ProjectChat\n                onCreateDocument={handleCreateNewDocument}\n                onCreateTemplateDocument={handleCreateTemplateDocument}\n              />\n            ) : currentSection === 'project-chat-editor' ? (\n              <DocumentEditor\n                currentSection=\"project-chat\" // 传递project-chat给DocumentEditor\n                onDocumentStateChange={handleDocumentStateChange}\n                onDocumentUpload={handleDocumentUpload}\n                selectedDocumentFromSidebar={selectedDocument}\n                onEditorReady={wrappedSetInsertTextFunction} // 传递编辑器就绪回调\n              />\n            ) : currentSection === 'pdf-chat' ? (\n              <DocumentEditor\n                currentSection={currentSection}\n                onDocumentStateChange={handleDocumentStateChange}\n                onDocumentUpload={handleDocumentUpload}\n                selectedDocumentFromSidebar={selectedDocument}\n                onEditorReady={wrappedSetInsertTextFunction} // 传递编辑器就绪回调\n              />\n            ) : currentSection === 'general-chat' ? (\n              <GeneralChat onStartChat={handleStartGeneralChat} />\n            ) : currentSection === 'knowledge-manager' ? (\n              <KnowledgeManager />\n            ) : currentSection === 'settings' ? (\n              <SettingsPage onPromptChange={handlePromptChange} />\n            ) : currentSection === 'dify-test' ? (\n              <DifyTest />\n            ) : currentSection === 'workflow-config' ? (\n              <WorkflowConfig />\n            ) : (\n              <DocumentEditor\n                currentSection={currentSection}\n                onDocumentStateChange={handleDocumentStateChange}\n                onDocumentUpload={handleDocumentUpload}\n                selectedDocumentFromSidebar={selectedDocument}\n                onEditorReady={wrappedSetInsertTextFunction} // 传递编辑器就绪回调\n              />\n            )}\n          </Box>\n        </Box>\n\n        {/* 右侧AI对话区 - 只在文档打开且可见时显示，公文生成模式下隐藏 */}\n        {isDocumentOpen && aiChatVisible && !currentSection.startsWith('project-chat') && (\n          <AIChat\n            currentSection={currentSection}\n            width={aiChatWidth}\n            onWidthChange={setAiChatWidth}\n            selectedDocument={selectedDocument}\n            onToggleVisibility={() => setAiChatVisible(!aiChatVisible)}\n            insertTextFunction={insertTextFunction} // 传递插入文本函数\n          />\n        )}\n\n        {/* AI聊天面板隐藏时的切换按钮 - 公文生成模式下不显示 */}\n        {isDocumentOpen && !aiChatVisible && !currentSection.startsWith('project-chat') && (\n          <Box\n            onClick={() => setAiChatVisible(true)}\n            sx={{\n              position: 'fixed',\n              right: 0,\n              top: '50%',\n              transform: 'translateY(-50%)',\n              width: '40px',\n              height: '100px',\n              backgroundColor: '#f3f4f6',\n              border: '1px solid #e5e7eb',\n              borderRight: 'none',\n              borderRadius: '8px 0 0 8px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              zIndex: 1000,\n              '&:hover': {\n                backgroundColor: '#e5e7eb',\n              },\n              transition: 'background-color 0.2s ease',\n            }}\n          >\n            <Typography\n              sx={{\n                writingMode: 'vertical-rl',\n                textOrientation: 'mixed',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                fontWeight: 500,\n              }}\n            >\n              AI聊天\n            </Typography>\n          </Box>\n        )}\n      </Box>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,SAASC,WAAW,EAAEC,GAAG,EAAEC,UAAU,QAAQ,eAAe;AAE5D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,gBAAgB,MAAM,+BAA+B;AAE5D;AACA,SAASC,aAAa,QAAQ,cAAc;AAC5C,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAYA;AACA,MAAMC,KAAK,GAAGhB,WAAW,CAAC;EACxBiB,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MAAE;MACjBC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAE,SAAS;MAAE;MACjBC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAAE;MACpBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,OAAO,EAAE,SAAS;MAAE;MACpBI,SAAS,EAAE,SAAS,CAAE;IACxB,CAAC;IACDK,IAAI,EAAE;MACJ,EAAE,EAAE,SAAS;MACb,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE,SAAS;MACd,GAAG,EAAE;IACP;EACF,CAAC;EACDC,UAAU,EAAE;IACVC,UAAU,EAAE,qDAAqD;IACjEC,EAAE,EAAE;MACFC,QAAQ,EAAE,SAAS;MACnBC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE;IACd,CAAC;IACDC,EAAE,EAAE;MACFH,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE;IACd,CAAC;IACDE,EAAE,EAAE;MACFJ,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE;IACd,CAAC;IACDG,EAAE,EAAE;MACFL,QAAQ,EAAE,SAAS;MACnBC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE;IACd,CAAC;IACDI,EAAE,EAAE;MACFN,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE;IACd,CAAC;IACDK,EAAE,EAAE;MACFP,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE;IACd,CAAC;IACDM,KAAK,EAAE;MACLR,QAAQ,EAAE,MAAM;MAChBE,UAAU,EAAE;IACd,CAAC;IACDO,KAAK,EAAE;MACLT,QAAQ,EAAE,UAAU;MACpBE,UAAU,EAAE;IACd,CAAC;IACDQ,OAAO,EAAE;MACPV,QAAQ,EAAE,SAAS;MACnBE,UAAU,EAAE;IACd;EACF,CAAC;EACDS,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB,CAAC;EACDC,OAAO,EAAE,CACP,MAAM,EACN,iEAAiE,EACjE,uEAAuE,EACvE,yEAAyE,EACzE,2EAA2E,EAC3E,uCAAuC,EACvC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC,CACnB;EACRC,UAAU,EAAE;IACVC,SAAS,EAAE;MACTC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,aAAa,EAAE,MAAM;UACrBnB,UAAU,EAAE,GAAG;UACfW,YAAY,EAAE,CAAC;UACfS,OAAO,EAAE;QACX,CAAC;QACDC,SAAS,EAAE;UACTC,SAAS,EAAE,iEAAiE;UAC5E,SAAS,EAAE;YACTA,SAAS,EAAE;UACb;QACF;MACF;IACF,CAAC;IACDC,QAAQ,EAAE;MACRN,cAAc,EAAE;QACdC,IAAI,EAAE;UACJM,eAAe,EAAE;QACnB;MACF;IACF,CAAC;IACDC,YAAY,EAAE;MACZR,cAAc,EAAE;QACdC,IAAI,EAAE;UACJ,0BAA0B,EAAE;YAC1BP,YAAY,EAAE;UAChB;QACF;MACF;IACF,CAAC;IACDe,cAAc,EAAE;MACdT,cAAc,EAAE;QACd;QACA,sBAAsB,EAAE;UACtBU,KAAK,EAAE,KAAK;UACZpC,UAAU,EAAE;QACd,CAAC;QACD,4BAA4B,EAAE;UAC5BA,UAAU,EAAE;QACd,CAAC;QACD,4BAA4B,EAAE;UAC5BA,UAAU,EAAE;QACd,CAAC;QACD;QACA,GAAG,EAAE;UACHqC,cAAc,EAAE;QAClB,CAAC;QACD;QACA,YAAY,EAAE;UACZA,cAAc,EAAE,MAAM;UACtBC,QAAQ,EAAE,MAAM,CAAE;QACpB;MACF;IACF;EACF;AACF,CAAC,CAAC;AAIF,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAC,UAAU,CAAC;EAChE,MAAM,CAACqE,cAAc,EAAEC,iBAAiB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxE,QAAQ,CAAqB,EAAE,CAAC;EAClF,MAAM,CAACyE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1E,QAAQ,CAA0B,IAAI,CAAC;EACvF,MAAM,CAAC2E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6E,WAAW,EAAEC,cAAc,CAAC,GAAG9E,QAAQ,CAAC,GAAG,CAAC;EACnD,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACiF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlF,QAAQ,CAAkC,IAAI,CAAC,CAAC,CAAC;;EAErG;EACA,MAAMmF,4BAA4B,GAAIC,EAAmC,IAAK;IAC5EC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/CD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAOF,EAAE,CAAC;IACvCC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAACF,EAAE,CAAC;IAClCF,qBAAqB,CAACE,EAAE,CAAC;;IAEzB;IACAG,UAAU,CAAC,MAAM;MACfF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAC7CE,MAAM,EAAE,CAAC,CAACP,kBAAkB;QAC5BQ,IAAI,EAAE,OAAOR;MACf,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAGD,MAAMS,sBAAsB,GAAIC,OAAe,IAAK;IAClD;IACA,IAAIA,OAAO,KAAK,UAAU,IAAIxB,cAAc,KAAK,UAAU,EAAE;MAC3D;MACAO,mBAAmB,CAAC,IAAI,CAAC;MACzBJ,iBAAiB,CAAC,KAAK,CAAC;MACxBU,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;MACxBE,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/B,CAAC,MAAM,IAAIS,OAAO,KAAK,cAAc,IAAIxB,cAAc,KAAK,cAAc,IAAIA,cAAc,KAAK,qBAAqB,EAAE;MACtH;MACAO,mBAAmB,CAAC,IAAI,CAAC;MACzBJ,iBAAiB,CAAC,KAAK,CAAC;MACxBU,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;MACxBE,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/B,CAAC,MAAM,IAAIS,OAAO,KAAK,cAAc,IAAIxB,cAAc,KAAK,cAAc,EAAE;MAC1E;MACAO,mBAAmB,CAAC,IAAI,CAAC;MACzBJ,iBAAiB,CAAC,KAAK,CAAC;MACxBU,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;MACxBE,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/B,CAAC,MAAM,IAAIS,OAAO,KAAK,UAAU,IAAIxB,cAAc,KAAK,UAAU,EAAE;MAClE;MACAO,mBAAmB,CAAC,IAAI,CAAC;MACzBJ,iBAAiB,CAAC,KAAK,CAAC;MACxBU,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;MACxBE,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/B;IAEAd,iBAAiB,CAACuB,OAAO,CAAC;EAC5B,CAAC;EAED,MAAMC,yBAAyB,GAAIC,MAAe,IAAK;IACrDvB,iBAAiB,CAACuB,MAAM,CAAC;EAC3B,CAAC;EAED,MAAMC,oBAAoB,GAAIC,QAA0B,IAAK;IAC3DV,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAES,QAAQ,CAAC,CAAC,CAAC;;IAEhE;IACAvB,oBAAoB,CAACwB,IAAI,IAAI,CAACD,QAAQ,EAAE,GAAGC,IAAI,CAAC,CAAC;;IAEjD;IACAtB,mBAAmB,CAACqB,QAAQ,CAAC;IAC7BzB,iBAAiB,CAAC,IAAI,CAAC;IACvBF,iBAAiB,CAAC,UAAU,CAAC;IAE7BiB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAMW,oBAAoB,GAAIF,QAA0B,IAAK;IAC3DrB,mBAAmB,CAACqB,QAAQ,CAAC;IAC7BzB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM4B,oBAAoB,GAAG,MAAOC,UAAkB,IAAK;IACzD,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACtF,aAAa,CAAC,UAAUoF,UAAU,EAAE,CAAC,EAAE;QAClEG,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,IAAIF,QAAQ,CAACG,EAAE,EAAE;QACf;QACA/B,oBAAoB,CAACwB,IAAI,IAAIA,IAAI,CAACQ,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKP,UAAU,CAAC,CAAC;;QAEvE;QACA,IAAI,CAAA1B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEiC,EAAE,MAAKP,UAAU,EAAE;UACvCzB,mBAAmB,CAAC,IAAI,CAAC;UACzBJ,iBAAiB,CAAC,KAAK,CAAC;QAC1B;QAEAe,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACvB,CAAC,MAAM;QACLD,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEP,QAAQ,CAACQ,UAAU,CAAC;QAC7CC,KAAK,CAAC,aAAa,CAAC;MACtB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCE,KAAK,CAAC,gBAAgB,CAAC;IACzB;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChClC,mBAAmB,CAACoB,IAAI,IAAI,CAACA,IAAI,CAAC;EACpC,CAAC;;EAED;EACA,MAAMe,oBAAoB,GAAGA,CAAA,KAAM;IACjC1B,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC,CAAC,CAAC;;IAE1D;IACA,MAAM0B,SAAS,GAAGjB,QAAQ,CAACkB,aAAa,CAAC,OAAO,CAAC;IACjDD,SAAS,CAACvB,IAAI,GAAG,MAAM;IACvBuB,SAAS,CAACE,MAAM,GAAG,gEAAgE;IACnFF,SAAS,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;;IAEhC;IACAJ,SAAS,CAACK,QAAQ,GAAG,MAAOC,KAAK,IAAK;MAAA,IAAAC,aAAA;MACpC,MAAMC,MAAM,GAAGF,KAAK,CAACE,MAA0B;MAC/C,MAAMC,IAAI,IAAAF,aAAA,GAAGC,MAAM,CAACE,KAAK,cAAAH,aAAA,uBAAZA,aAAA,CAAe,CAAC,CAAC;MAE9B,IAAIE,IAAI,EAAE;QACRpC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEmC,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC;;QAE1C,IAAI;UACF;UACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;UAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;UAE7B,MAAMrB,QAAQ,GAAG,MAAMC,KAAK,CAACtF,aAAa,CAAC,SAAS,CAAC,EAAE;YACrDuF,MAAM,EAAE,MAAM;YACdyB,IAAI,EAAEH;UACR,CAAC,CAAC;UAEF,IAAIxB,QAAQ,CAACG,EAAE,EAAE;YACf,MAAMyB,MAAM,GAAG,MAAM5B,QAAQ,CAAC6B,IAAI,CAAC,CAAC;YACpC5C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE0C,MAAM,CAAC,CAAC,CAAC;;YAE1C;YACA,IAAIE,eAAe,GAAG,EAAE;YACxB,IAAI;cACF7C,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;cAClCD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE;gBAC3B6C,QAAQ,EAAEV,IAAI,CAACE,IAAI;gBACnBS,QAAQ,EAAEX,IAAI,CAAChC,IAAI;gBACnB4C,QAAQ,EAAEZ,IAAI,CAACa,IAAI;gBACnBC,QAAQ,EAAEP,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACtB,EAAE;gBACpC+B,UAAU,EAAET;cACd,CAAC,CAAC;;cAEF;cACA,MAAMU,UAAU,GAAG3H,aAAa,CAAC,YAAYiH,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACtB,EAAE,EAAE,CAAC;cAC1ErB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEoD,UAAU,CAAC;cAE7C,MAAMC,eAAe,GAAG,MAAMtC,KAAK,CAACqC,UAAU,CAAC;cAC/CrD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqD,eAAe,CAACC,MAAM,CAAC;cAEzD,IAAID,eAAe,CAACpC,EAAE,EAAE;gBACtB2B,eAAe,GAAG,MAAMS,eAAe,CAAC9G,IAAI,CAAC,CAAC;gBAC9CwD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4C,eAAe,CAACW,MAAM,CAAC;gBAC9DxD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE4C,eAAe,CAACY,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;cACzE,CAAC,MAAM;gBACL,MAAMC,SAAS,GAAG,MAAMJ,eAAe,CAAC9G,IAAI,CAAC,CAAC;gBAC9CwD,OAAO,CAAC2D,IAAI,CAAC,sBAAsB,EAAEL,eAAe,CAACC,MAAM,EAAE,KAAK,EAAEG,SAAS,CAAC;cAChF;YACF,CAAC,CAAC,OAAOpC,KAAK,EAAE;cACdtB,OAAO,CAACsB,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;YAC3C;;YAEA;YACA,MAAMsC,WAA6B,GAAG;cACpCvC,EAAE,EAAEsB,MAAM,CAACQ,MAAM,IAAIR,MAAM,CAACtB,EAAE;cAAE;cAChCwC,KAAK,EAAEzB,IAAI,CAACE,IAAI;cAAE;cAClBlC,IAAI,EAAEgC,IAAI,CAAChC,IAAI,IAAI,0BAA0B;cAC7C6C,IAAI,EAAE,GAAG,CAACb,IAAI,CAACa,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEa,OAAO,CAAC,CAAC,CAAC,KAAK;cAClDC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC;cACtBC,WAAW,EAAE,OAAO,IAAID,IAAI,CAAC,CAAC,CAACE,cAAc,CAAC,CAAC,EAAE;cACjDC,OAAO,EAAEtB,eAAe,CAAC;YAC3B,CAAC;;YAED;YACA1D,oBAAoB,CAACwB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEiD,WAAW,CAAC,CAAC;;YAEpD;YACAvE,mBAAmB,CAACuE,WAAW,CAAC;YAChC3E,iBAAiB,CAAC,IAAI,CAAC;YACvBF,iBAAiB,CAAC,UAAU,CAAC;YAE7BiB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC,CAAC,CAAC;UACpC,CAAC,MAAM;YACLD,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEP,QAAQ,CAACQ,UAAU,CAAC;YAC/CC,KAAK,CAAC,aAAa,CAAC;UACtB;QACF,CAAC,CAAC,OAAOF,KAAK,EAAE;UACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClCE,KAAK,CAAC,gBAAgB,CAAC;QACzB;MACF;;MAEA;MACAd,QAAQ,CAACgC,IAAI,CAAC0B,WAAW,CAACzC,SAAS,CAAC;IACtC,CAAC;;IAED;IACAjB,QAAQ,CAACgC,IAAI,CAAC2B,WAAW,CAAC1C,SAAS,CAAC;IACpCA,SAAS,CAAC2C,KAAK,CAAC,CAAC;EACnB,CAAC;;EAID;EACA,MAAMC,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1CvE,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC,CAAC,CAAC;;IAE7D,IAAI;MACF;MACA,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAACtF,aAAa,CAAC,kBAAkB,CAAC,EAAE;QAC9DuF,MAAM,EAAE,MAAM;QACduD,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACD9B,IAAI,EAAE+B,IAAI,CAACC,SAAS,CAAC;UACnBtE,IAAI,EAAE,MAAM;UAAE;UACdyD,KAAK,EAAE,QAAQ,IAAIG,IAAI,CAAC,CAAC,CAACW,kBAAkB,CAAC,CAAC,IAAI,IAAIX,IAAI,CAAC,CAAC,CAACY,kBAAkB,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;QACtG,CAAC;MACH,CAAC,CAAC;MAEF,IAAI9D,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMyB,MAAM,GAAG,MAAM5B,QAAQ,CAAC6B,IAAI,CAAC,CAAC;QACpC5C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE0C,MAAM,CAAC,CAAC,CAAC;;QAExC;QACA,MAAMiB,WAA6B,GAAG;UACpCvC,EAAE,EAAEsB,MAAM,CAACtB,EAAE,IAAI2C,IAAI,CAACc,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UACtClB,KAAK,EAAElB,MAAM,CAACkB,KAAK,IAAI,QAAQ,IAAIG,IAAI,CAAC,CAAC,CAACW,kBAAkB,CAAC,CAAC,EAAE;UAChEvE,IAAI,EAAE,MAAM;UACZ6C,IAAI,EAAEN,MAAM,CAACM,IAAI,GAAG,GAAG,CAACN,MAAM,CAACM,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEa,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,SAAS;UAC9EC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC;UACtBC,WAAW,EAAE,OAAO,IAAID,IAAI,CAAC,CAAC,CAACE,cAAc,CAAC,CAAC,EAAE;UACjDc,MAAM,EAAE,cAAc;UACtBb,OAAO,EAAExB,MAAM,CAACwB,OAAO,IAAI;QAC7B,CAAC;;QAED;QACAhF,oBAAoB,CAACwB,IAAI,IAAI,CAACiD,WAAW,EAAE,GAAGjD,IAAI,CAAC,CAAC;;QAEpD;QACAtB,mBAAmB,CAACuE,WAAW,CAAC;QAChC3E,iBAAiB,CAAC,IAAI,CAAC;QACvBU,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;QACxBZ,iBAAiB,CAAC,qBAAqB,CAAC,CAAC,CAAC;;QAE1CiB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC;MACrC,CAAC,MAAM;QACLD,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEP,QAAQ,CAACQ,UAAU,CAAC;QAC7CC,KAAK,CAAC,aAAa,CAAC;MACtB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BE,KAAK,CAAC,YAAY,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMyD,4BAA4B,GAAG,MAAAA,CAAA,KAAY;IAC/CjF,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC,CAAC,CAAC;;IAEpE,IAAI;MACF;MACA,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAACtF,aAAa,CAAC,kBAAkB,CAAC,EAAE;QAC9DuF,MAAM,EAAE,MAAM;QACduD,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACD9B,IAAI,EAAE+B,IAAI,CAACC,SAAS,CAAC;UACnBtE,IAAI,EAAE,MAAM;UACZ8E,QAAQ,EAAE,UAAU;UAAE;UACtBrB,KAAK,EAAE,MAAM,IAAIG,IAAI,CAAC,CAAC,CAACW,kBAAkB,CAAC,CAAC,IAAI,IAAIX,IAAI,CAAC,CAAC,CAACY,kBAAkB,CAAC,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;QACpG,CAAC;MACH,CAAC,CAAC;MAEF,IAAI9D,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMyB,MAAM,GAAG,MAAM5B,QAAQ,CAAC6B,IAAI,CAAC,CAAC;QACpC5C,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0C,MAAM,CAAC,CAAC,CAAC;;QAE5C;QACA,MAAMiB,WAA6B,GAAG;UACpCvC,EAAE,EAAEsB,MAAM,CAACtB,EAAE,IAAI2C,IAAI,CAACc,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UACtClB,KAAK,EAAElB,MAAM,CAACkB,KAAK,IAAI,MAAM,IAAIG,IAAI,CAAC,CAAC,CAACW,kBAAkB,CAAC,CAAC,EAAE;UAC9DvE,IAAI,EAAE,MAAM;UACZ6C,IAAI,EAAEN,MAAM,CAACM,IAAI,GAAG,GAAG,CAACN,MAAM,CAACM,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEa,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,SAAS;UAC9EC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC;UACtBC,WAAW,EAAE,WAAW,IAAID,IAAI,CAAC,CAAC,CAACE,cAAc,CAAC,CAAC,EAAE;UACrDc,MAAM,EAAE,cAAc;UACtBb,OAAO,EAAExB,MAAM,CAACwB,OAAO,IAAI;QAC7B,CAAC;;QAED;QACAhF,oBAAoB,CAACwB,IAAI,IAAI,CAACiD,WAAW,EAAE,GAAGjD,IAAI,CAAC,CAAC;;QAEpD;QACAtB,mBAAmB,CAACuE,WAAW,CAAC;QAChC3E,iBAAiB,CAAC,IAAI,CAAC;QACvBU,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;QACxBZ,iBAAiB,CAAC,qBAAqB,CAAC,CAAC,CAAC;;QAE1CiB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC;;QAEvC;QACAC,UAAU,CAAC,MAAM;UACf,MAAMiF,WAAW,GAAGzE,QAAQ,CAAC0E,aAAa,CAAC,2BAA2B,CAAsB;UAC5F,IAAID,WAAW,IAAIA,WAAW,CAACE,aAAa,EAAE;YAC5C,MAAMC,OAAO,GAAG;cACdlF,IAAI,EAAE,uBAAuB;cAC7B8E,QAAQ,EAAE;YACZ,CAAC;YACDC,WAAW,CAACE,aAAa,CAACE,WAAW,CAACD,OAAO,EAAE,GAAG,CAAC;YACnDtF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC7C;QACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ,CAAC,MAAM;QACLD,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEP,QAAQ,CAACQ,UAAU,CAAC;QACjDC,KAAK,CAAC,iBAAiB,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCE,KAAK,CAAC,gBAAgB,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMgE,sBAAsB,GAAGA,CAACF,OAAe,EAAEvJ,IAAY,KAAK;IAChEiE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MAAEqF,OAAO;MAAEvJ;IAAK,CAAC,CAAC;IACzC;IACA;EACF,CAAC;;EAED;EACA,MAAM0J,kBAAkB,GAAIC,MAAW,IAAK;IAC1C1F,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEyF,MAAM,CAAC;IAChC;IACA;EACF,CAAC;EAED,oBACE9J,OAAA,CAAChB,aAAa;IAACiB,KAAK,EAAEA,KAAM;IAAA8J,QAAA,gBAC1B/J,OAAA,CAACd,WAAW;MAAAgI,QAAA,EAAA8C,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACflK,OAAA,CAACb,GAAG;MACFgL,SAAS,EAAC,WAAW;MACrBC,EAAE,EAAE;QACFjE,OAAO,EAAE,MAAM;QACfkE,mBAAmB;QACjB;QACAnH,cAAc,CAACoH,UAAU,CAAC,cAAc,CAAC,GACrC,GAAG5G,gBAAgB,GAAG,MAAM,GAAG,OAAO;QACxC;QAAA,EACGN,cAAc,IAAIU,aAAa,GAC9B,GAAGJ,gBAAgB,GAAG,MAAM,GAAG,OAAO,QAAQE,WAAW,IAAI,GAC7D,GAAGF,gBAAgB,GAAG,MAAM,GAAG,OAAO,MAAO;QACrD6G,MAAM,EAAE,OAAO;QACf1H,KAAK,EAAE,OAAO;QACd2H,QAAQ,EAAE,OAAO;QACjBzH,QAAQ,EAAE,QAAQ;QAClB0H,eAAe,EAAE;MACnB,CAAE;MAAAV,QAAA,gBAGF/J,OAAA,CAACX,OAAO;QACNqL,kBAAkB,EAAEjG,sBAAuB;QAC3CnB,iBAAiB,EAAEA,iBAAkB;QACrCqH,gBAAgB,EAAE3F,oBAAqB;QACvC4F,gBAAgB,EAAE3F,oBAAqB;QACvC4F,gBAAgB,EAAE/E,oBAAqB,CAAC;QAAA;QACxCgF,SAAS,EAAEpH,gBAAiB;QAC5BqH,gBAAgB,EAAElF;MAAoB;QAAAqB,QAAA,EAAA8C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAGFlK,OAAA,CAACb,GAAG;QACFiL,EAAE,EAAE;UACFK,eAAe,EAAE,SAAS;UAC1BtE,OAAO,EAAE,MAAM;UACf6E,aAAa,EAAE,QAAQ;UACvBT,MAAM,EAAE,MAAM;UACd;UACA1H,KAAK,EAAEK,cAAc,CAACoH,UAAU,CAAC,cAAc,CAAC,GAAG,MAAM,GAAG,MAAM;UAClEE,QAAQ,EAAEtH,cAAc,CAACoH,UAAU,CAAC,cAAc,CAAC,GAAG,MAAM,GAAG,MAAM;UACrEvH,QAAQ,EAAE,QAAQ;UAClBkI,YAAY,EAAG7H,cAAc,IAAI,CAACF,cAAc,CAACoH,UAAU,CAAC,cAAc,CAAC,GAAI,CAAC,GAAG;QACrF,CAAE;QAAAP,QAAA,eAEF/J,OAAA,CAACb,GAAG;UACFiL,EAAE,EAAE;YACFvH,KAAK,EAAE,MAAM;YACb0H,MAAM,EAAE,MAAM;YACdC,QAAQ,EAAE,MAAM;YAChBC,eAAe,EAAE,OAAO;YACxB1H,QAAQ,EAAEG,cAAc,KAAK,UAAU,GAAG,MAAM,GAAG,QAAQ;YAC3DiD,OAAO,EAAE,MAAM;YACf6E,aAAa,EAAE,QAAQ;YACvB;YACA;YACA;YACA,IAAI5H,cAAc,IAAIF,cAAc,CAACoH,UAAU,CAAC,cAAc,CAAC,GAAG;cAChEzI,YAAY,EAAE,CAAC;cACfqJ,MAAM,EAAE,CAAC;cACT1I,SAAS,EAAE;YACb,CAAC,GAAG;cACFX,YAAY,EAAE,CAAC;cACfW,SAAS,EAAE,uEAAuE;cAClF2I,SAAS,EAAE,CAAC;cACZC,WAAW,EAAE,CAAC;cACdC,YAAY,EAAE,CAAC;cACfC,UAAU,EAAE,CAAC;cAAG;cAChBC,SAAS,EAAE,oBAAoB;cAC/B;cACA1I,KAAK,EAAE,mBAAmB;cAAE;cAC5B2H,QAAQ,EAAE;YACZ,CAAC;UACH,CAAE;UAAAT,QAAA,EAED7G,cAAc,KAAK,cAAc,gBAChClD,OAAA,CAACR,WAAW;YACVgM,gBAAgB,EAAE7C,uBAAwB;YAC1C8C,wBAAwB,EAAEpC;UAA6B;YAAAnC,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,GACAhH,cAAc,KAAK,qBAAqB,gBAC1ClD,OAAA,CAACV,cAAc;YACb4D,cAAc,EAAC,cAAc,CAAC;YAAA;YAC9BwI,qBAAqB,EAAE/G,yBAA0B;YACjDgH,gBAAgB,EAAE9G,oBAAqB;YACvC+G,2BAA2B,EAAEpI,gBAAiB;YAC9CqI,aAAa,EAAE3H,4BAA6B,CAAC;UAAA;YAAAgD,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,GACAhH,cAAc,KAAK,UAAU,gBAC/BlD,OAAA,CAACV,cAAc;YACb4D,cAAc,EAAEA,cAAe;YAC/BwI,qBAAqB,EAAE/G,yBAA0B;YACjDgH,gBAAgB,EAAE9G,oBAAqB;YACvC+G,2BAA2B,EAAEpI,gBAAiB;YAC9CqI,aAAa,EAAE3H,4BAA6B,CAAC;UAAA;YAAAgD,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,GACAhH,cAAc,KAAK,cAAc,gBACnClD,OAAA,CAACP,WAAW;YAACqM,WAAW,EAAElC;UAAuB;YAAA1C,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClDhH,cAAc,KAAK,mBAAmB,gBACxClD,OAAA,CAACH,gBAAgB;YAAAqH,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBhH,cAAc,KAAK,UAAU,gBAC/BlD,OAAA,CAACN,YAAY;YAACqM,cAAc,EAAElC;UAAmB;YAAA3C,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClDhH,cAAc,KAAK,WAAW,gBAChClD,OAAA,CAACL,QAAQ;YAAAuH,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GACVhH,cAAc,KAAK,iBAAiB,gBACtClD,OAAA,CAACJ,cAAc;YAAAsH,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAElBlK,OAAA,CAACV,cAAc;YACb4D,cAAc,EAAEA,cAAe;YAC/BwI,qBAAqB,EAAE/G,yBAA0B;YACjDgH,gBAAgB,EAAE9G,oBAAqB;YACvC+G,2BAA2B,EAAEpI,gBAAiB;YAC9CqI,aAAa,EAAE3H,4BAA6B,CAAC;UAAA;YAAAgD,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QACF;UAAAhD,QAAA,EAAA8C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAhD,QAAA,EAAA8C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL9G,cAAc,IAAIU,aAAa,IAAI,CAACZ,cAAc,CAACoH,UAAU,CAAC,cAAc,CAAC,iBAC5EtK,OAAA,CAACT,MAAM;QACL2D,cAAc,EAAEA,cAAe;QAC/BL,KAAK,EAAEe,WAAY;QACnBoI,aAAa,EAAEnI,cAAe;QAC9BL,gBAAgB,EAAEA,gBAAiB;QACnCyI,kBAAkB,EAAEA,CAAA,KAAMlI,gBAAgB,CAAC,CAACD,aAAa,CAAE;QAC3DE,kBAAkB,EAAEA,kBAAmB,CAAC;MAAA;QAAAkD,QAAA,EAAA8C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CACF,EAGA9G,cAAc,IAAI,CAACU,aAAa,IAAI,CAACZ,cAAc,CAACoH,UAAU,CAAC,cAAc,CAAC,iBAC7EtK,OAAA,CAACb,GAAG;QACF+M,OAAO,EAAEA,CAAA,KAAMnI,gBAAgB,CAAC,IAAI,CAAE;QACtCqG,EAAE,EAAE;UACF+B,QAAQ,EAAE,OAAO;UACjBC,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE,KAAK;UACVC,SAAS,EAAE,kBAAkB;UAC7BzJ,KAAK,EAAE,MAAM;UACb0H,MAAM,EAAE,OAAO;UACfE,eAAe,EAAE,SAAS;UAC1B8B,MAAM,EAAE,mBAAmB;UAC3BC,WAAW,EAAE,MAAM;UACnB3K,YAAY,EAAE,aAAa;UAC3BsE,OAAO,EAAE,MAAM;UACfsG,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,MAAM,EAAE,SAAS;UACjBC,MAAM,EAAE,IAAI;UACZ,SAAS,EAAE;YACTnC,eAAe,EAAE;UACnB,CAAC;UACDoC,UAAU,EAAE;QACd,CAAE;QAAA9C,QAAA,eAEF/J,OAAA,CAACZ,UAAU;UACTgL,EAAE,EAAE;YACF0C,WAAW,EAAE,aAAa;YAC1BC,eAAe,EAAE,OAAO;YACxB9L,QAAQ,EAAE,UAAU;YACpB+L,KAAK,EAAE,SAAS;YAChB9L,UAAU,EAAE;UACd,CAAE;UAAA6I,QAAA,EACH;QAED;UAAA7C,QAAA,EAAA8C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAhD,QAAA,EAAA8C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAhD,QAAA,EAAA8C,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAhD,QAAA,EAAA8C,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACjH,EAAA,CA7gBQD,GAAG;AAAAiK,EAAA,GAAHjK,GAAG;AA+gBZ,eAAeA,GAAG;AAAC,IAAAiK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}