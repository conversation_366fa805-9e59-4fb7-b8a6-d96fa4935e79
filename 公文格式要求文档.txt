公文格式要求文档 - 基于标准模板的HTML输出规范

本文档基于模板.docx（标准公文）和模板1.docx（述职报告）制定，用于指导AI生成符合格式要求的HTML内容。

=== 标准公文格式要求（基于模板.docx）===

一、版头部分格式要求
1. 份号：HTML标签<div class="fenhao">，内容为6位数字，如000001
2. 密级：HTML标签<div class="miji">，内容格式为"××★保密期限"，如"秘密★1年"
3. 紧急程度：HTML标签<div class="jinji">，内容为"特急"、"急件"或"普通"
4. 发文机关标志：HTML标签<div class="jiguan">，内容格式为"机关名称文件"，如"××市人民政府办公室文件"
5. 发文字号：HTML标签<div class="fawenhao">，内容格式为"机关简称〔年份〕号"，如"市政办〔2024〕15号"
6. 签发人：HTML标签<div class="qianfaren">，内容格式为"签发人：姓名一 姓名二"
7. 红色分隔线：HTML标签<div class="red-line">，内容为"————————————————————————————————"

二、主体部分格式要求
1. 标题：HTML标签<div class="title">，内容格式为"关于××××的通知/请示/报告"
2. 主送机关：HTML标签<div class="zhusong">，内容格式为"机关名称："，如"各区县政府，市政府各部门："
3. 正文段落：HTML标签<div class="content">，每个段落一个div，使用序号"一、二、三"或"（一）（二）（三）"或"1.2.3."
4. 发文机关署名：HTML标签<div class="signature">，内容为发文机关全称
5. 成文日期：HTML标签<div class="date">，内容格式为"xxxx年xx月xx日"

三、版记部分格式要求
1. 抄送机关：HTML标签<div class="chaosong">，内容格式为"抄送：机关1，机关2，机关3。"
2. 印发信息：HTML标签<div class="yinfa">，内容格式为"印发机关 xxxx年xx月xx日印发"

=== 述职报告格式要求（基于模板1.docx）===

一、文档结构格式要求
1. 标题：HTML标签<div class="title">，内容格式为"xxxx年度个人述职述廉报告"
2. 称谓：HTML标签<div class="greeting">，内容为"尊敬的各位领导、同事们："
3. 开场白：HTML标签<div class="content">，说明报告背景和目的

二、主体内容层次结构
1. 一级标题：HTML标签<div class="level1">，使用"一、二、三、四"序号
2. 二级标题：HTML标签<div class="level2">，使用"（一）（二）（三）"序号
3. 三级标题：HTML标签<div class="level3">，使用"1.2.3.4.5."序号
4. 正文内容：HTML标签<div class="content">，具体的工作内容描述

三、结尾部分格式要求
1. 署名：HTML标签<div class="signature">，内容格式为"发言人：姓名"
2. 日期：HTML标签<div class="date">，内容格式为"日期：xxxx年xx月xx日"

=== 文档类型识别关键词 ===

标准公文关键词：通知、请示、报告、决定、公告、通告、意见、办法、函、批复、会议纪要、关于、发文机关、主送机关、抄送
述职报告关键词：述职、述廉、工作总结、个人总结、年度汇报、履职情况、个人报告

=== HTML输出质量要求 ===

1. 必须使用完整的HTML标签，每个div都要有正确的class属性
2. 绝对不能使用占位符，如[xxx]、××等，必须填入具体真实的内容
3. 内容要详实具体，符合公文写作规范
4. 序号层次要准确，不能混乱
5. 日期格式要统一，使用"xxxx年xx月xx日"格式
6. 语言要正式规范，符合公文语言风格

=== 模板参考示例 ===

标准公文HTML结构示例：
<div class="fenhao">000001</div>
<div class="miji">秘密★1年</div>
<div class="jinji">特急</div>
<div class="jiguan">××市人民政府办公室文件</div>
<div class="fawenhao">市政办〔2024〕15号</div>
<div class="qianfaren">签发人：张三 李四</div>
<div class="red-line">————————————————————————————————</div>
<div class="title">关于××××的通知</div>
<div class="zhusong">各相关单位：</div>
<div class="content">正文第一段内容...</div>
<div class="content">一、第一项要求...</div>
<div class="content">二、第二项要求...</div>
<div class="signature">发文机关名称</div>
<div class="date">xxxx年xx月xx日</div>
<div class="chaosong">抄送：相关机关。</div>
<div class="yinfa">印发机关 xxxx年xx月xx日印发</div>

述职报告HTML结构示例：
<div class="title">2024年度个人述职述廉报告</div>
<div class="greeting">尊敬的各位领导、同事们：</div>
<div class="content">开场白内容...</div>
<div class="level1">一、履职情况</div>
<div class="level2">（一）业务工作方面</div>
<div class="content">具体工作内容...</div>
<div class="level2">（二）重点工作方面</div>
<div class="content">重点工作内容...</div>
<div class="level1">二、廉洁自律情况</div>
<div class="content">廉洁自律具体表现...</div>
<div class="level1">三、存在问题</div>
<div class="content">问题分析...</div>
<div class="level1">四、改进措施</div>
<div class="level3">1.具体措施一</div>
<div class="level3">2.具体措施二</div>
<div class="signature">发言人：姓名</div>
<div class="date">日期：xxxx年xx月xx日</div>

=== 使用说明 ===

AI在生成任何文档前，必须：
1. 根据用户需求识别文档类型（标准公文或述职报告）
2. 选择对应的HTML格式结构
3. 严格按照格式要求组织内容
4. 确保所有HTML标签完整准确
5. 填入具体详实的内容，不使用占位符

本文档是AI生成HTML格式公文的标准参考，必须严格遵循执行。
