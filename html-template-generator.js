/**
 * HTML模板生成器
 * 基于模板.docx和模板1.docx的格式特点生成HTML模板
 */

class HTMLTemplateGenerator {
    constructor() {
        // 基于GB/T 9704-2012标准的样式定义
        this.officialDocumentStyles = {
            // 版头样式
            fenhao: 'font-family: "仿宋"; font-size: 16pt; text-align: left; margin: 0; padding: 0;',
            miji: 'font-family: "黑体"; font-size: 16pt; font-weight: bold; text-align: left; margin: 0; padding: 0;',
            jinji: 'font-family: "黑体"; font-size: 16pt; font-weight: bold; text-align: left; margin: 0; padding: 0;',
            jiguan: 'font-family: "小标宋"; font-size: 24pt; font-weight: bold; color: red; text-align: center; margin: 20pt 0 10pt 0; padding: 0;',
            fawenhao: 'font-family: "仿宋"; font-size: 16pt; text-align: center; margin: 10pt 0; padding: 0;',
            qianfaren: 'font-family: "仿宋"; font-size: 16pt; text-align: left; margin: 10pt 0; padding: 0;',
            
            // 红色分隔线
            redLine: 'color: red; text-align: center; margin: 10pt 0; padding: 0; font-size: 14pt;',
            
            // 主体样式
            title: 'font-family: "小标宋"; font-size: 22pt; font-weight: bold; text-align: center; margin: 20pt 0; padding: 0; line-height: 1.5;',
            zhusong: 'font-family: "仿宋"; font-size: 16pt; text-align: left; margin: 10pt 0; padding: 0;',
            content: 'font-family: "仿宋"; font-size: 16pt; text-align: left; text-indent: 2em; margin: 10pt 0; padding: 0; line-height: 1.5;',
            signature: 'font-family: "仿宋"; font-size: 16pt; text-align: right; margin: 20pt 0 10pt 0; padding: 0;',
            date: 'font-family: "仿宋"; font-size: 16pt; text-align: right; margin: 10pt 0; padding: 0;',
            
            // 版记样式
            chaosong: 'font-family: "仿宋"; font-size: 14pt; text-align: left; text-indent: 1em; margin: 10pt 0; padding: 0;',
            yinfa: 'font-family: "仿宋"; font-size: 14pt; text-align: left; margin: 10pt 0; padding: 0;'
        };

        // 述职报告样式定义
        this.reportStyles = {
            title: 'font-family: "黑体"; font-size: 18pt; font-weight: bold; text-align: center; margin: 20pt 0; padding: 0;',
            greeting: 'font-family: "仿宋"; font-size: 16pt; text-align: left; margin: 15pt 0; padding: 0;',
            level1: 'font-family: "黑体"; font-size: 16pt; font-weight: bold; text-align: left; margin: 15pt 0 10pt 0; padding: 0;',
            level2: 'font-family: "楷体"; font-size: 16pt; font-weight: bold; text-align: left; text-indent: 2em; margin: 10pt 0; padding: 0;',
            level3: 'font-family: "仿宋"; font-size: 16pt; text-align: left; text-indent: 2em; margin: 8pt 0; padding: 0;',
            content: 'font-family: "仿宋"; font-size: 16pt; text-align: left; text-indent: 2em; margin: 8pt 0; padding: 0; line-height: 1.5;',
            signature: 'font-family: "仿宋"; font-size: 16pt; text-align: left; margin: 20pt 0 10pt 0; padding: 0;',
            date: 'font-family: "仿宋"; font-size: 16pt; text-align: left; margin: 10pt 0; padding: 0;'
        };
    }

    /**
     * 生成标准公文HTML模板
     */
    generateOfficialDocumentTemplate() {
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>标准公文模板</title>
    <style>
        body { 
            margin: 0; 
            padding: 20pt; 
            font-family: "仿宋"; 
            line-height: 1.5;
            background: white;
        }
        .document-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 37mm 28mm 35mm 28mm; /* 国标页边距 */
        }
        .fenhao { ${this.officialDocumentStyles.fenhao} }
        .miji { ${this.officialDocumentStyles.miji} }
        .jinji { ${this.officialDocumentStyles.jinji} }
        .jiguan { ${this.officialDocumentStyles.jiguan} }
        .fawenhao { ${this.officialDocumentStyles.fawenhao} }
        .qianfaren { ${this.officialDocumentStyles.qianfaren} }
        .red-line { ${this.officialDocumentStyles.redLine} }
        .title { ${this.officialDocumentStyles.title} }
        .zhusong { ${this.officialDocumentStyles.zhusong} }
        .content { ${this.officialDocumentStyles.content} }
        .signature { ${this.officialDocumentStyles.signature} }
        .date { ${this.officialDocumentStyles.date} }
        .chaosong { ${this.officialDocumentStyles.chaosong} }
        .yinfa { ${this.officialDocumentStyles.yinfa} }
        .empty-line { height: 1em; }
    </style>
</head>
<body>
    <div class="document-container">
        <!-- 版头部分 -->
        <div class="fenhao">{{FENHAO}}</div>
        <div class="miji">{{MIJI}}</div>
        <div class="jinji">{{JINJI}}</div>
        <div class="empty-line"></div>
        <div class="jiguan">{{JIGUAN}}</div>
        <div class="empty-line"></div>
        <div class="fawenhao">{{FAWENHAO}}</div>
        <div class="qianfaren">{{QIANFAREN}}</div>
        <div class="red-line">————————————————————————————————</div>
        
        <!-- 主体部分 -->
        <div class="empty-line"></div>
        <div class="title">{{TITLE}}</div>
        <div class="empty-line"></div>
        <div class="zhusong">{{ZHUSONG}}</div>
        <div class="content">{{CONTENT}}</div>
        <div class="empty-line"></div>
        <div class="signature">{{SIGNATURE}}</div>
        <div class="date">{{DATE}}</div>
        
        <!-- 版记部分 -->
        <div class="empty-line"></div>
        <div class="chaosong">{{CHAOSONG}}</div>
        <div class="yinfa">{{YINFA}}</div>
    </div>
</body>
</html>`;
    }

    /**
     * 生成述职报告HTML模板
     */
    generateReportTemplate() {
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>述职报告模板</title>
    <style>
        body { 
            margin: 0; 
            padding: 20pt; 
            font-family: "仿宋"; 
            line-height: 1.5;
            background: white;
        }
        .document-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 30mm 25mm;
        }
        .title { ${this.reportStyles.title} }
        .greeting { ${this.reportStyles.greeting} }
        .level1 { ${this.reportStyles.level1} }
        .level2 { ${this.reportStyles.level2} }
        .level3 { ${this.reportStyles.level3} }
        .content { ${this.reportStyles.content} }
        .signature { ${this.reportStyles.signature} }
        .date { ${this.reportStyles.date} }
        .empty-line { height: 1em; }
    </style>
</head>
<body>
    <div class="document-container">
        <div class="title">{{TITLE}}</div>
        <div class="empty-line"></div>
        <div class="greeting">{{GREETING}}</div>
        <div class="content">{{OPENING}}</div>
        
        {{MAIN_CONTENT}}
        
        <div class="content">{{CLOSING}}</div>
        <div class="empty-line"></div>
        <div class="signature">{{SIGNATURE}}</div>
        <div class="date">{{DATE}}</div>
    </div>
</body>
</html>`;
    }

    /**
     * 为AI生成提示词模板
     */
    generateAIPromptTemplate(documentType) {
        if (documentType === 'official') {
            return `请按照以下HTML格式输出标准公文内容：

请严格按照以下格式输出，每个占位符用实际内容替换：

<div class="fenhao">000001</div>
<div class="miji">××★1年</div>
<div class="jinji">特急</div>
<div class="jiguan">[发文机关名称]文件</div>
<div class="fawenhao">[机关简称]〔2024〕×号</div>
<div class="qianfaren">签发人：[姓名一] [姓名二]</div>
<div class="red-line">————————————————————————————————</div>
<div class="title">[公文标题]</div>
<div class="zhusong">[主送机关]：</div>
<div class="content">[正文第一段内容]</div>
<div class="content">[正文第二段内容]</div>
<div class="content">[正文第三段内容]</div>
<div class="signature">[发文机关署名]</div>
<div class="date">[成文日期]</div>
<div class="chaosong">抄送：[抄送机关列表]</div>
<div class="yinfa">[印发机关] [印发日期]印发</div>

要求：
1. 保持HTML标签结构不变
2. 用实际内容替换方括号内的占位符
3. 确保内容符合公文写作规范
4. 日期格式：YYYY年M月D日`;

        } else if (documentType === 'report') {
            return `请按照以下HTML格式输出述职报告内容：

请严格按照以下格式输出，每个占位符用实际内容替换：

<div class="title">[报告标题]</div>
<div class="greeting">[称谓]：</div>
<div class="content">[开场白内容]</div>
<div class="level1">一、[第一部分标题]</div>
<div class="level2">(一) [第一小节标题]</div>
<div class="content">[第一小节内容]</div>
<div class="level2">(二) [第二小节标题]</div>
<div class="content">[第二小节内容]</div>
<div class="level1">二、[第二部分标题]</div>
<div class="level2">(一) [第一小节标题]</div>
<div class="content">[第一小节内容]</div>
<div class="level3">1. [具体措施一]</div>
<div class="level3">2. [具体措施二]</div>
<div class="content">[结语内容]</div>
<div class="signature">发言人：[发言人姓名]</div>
<div class="date">日期：[具体日期]</div>

要求：
1. 保持HTML标签结构不变
2. 用实际内容替换方括号内的占位符
3. 确保层次结构清晰
4. 内容要具体详实`;
        }
    }

    /**
     * 解析AI生成的HTML内容并转换为OnlyOffice格式
     */
    parseHTMLToOnlyOfficeFormat(htmlContent) {
        // 提取HTML中的内容和样式信息
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        
        const elements = [];
        const divs = doc.querySelectorAll('div[class]');
        
        divs.forEach(div => {
            const className = div.className;
            const content = div.textContent.trim();
            
            if (content) {
                elements.push({
                    type: className,
                    content: content,
                    style: this.getStyleForClass(className)
                });
            }
        });
        
        return elements;
    }

    /**
     * 根据CSS类名获取对应的样式信息
     */
    getStyleForClass(className) {
        const styleMap = {
            // 公文样式映射
            'fenhao': { font: '仿宋', size: 32, align: 'left', indent: 0 },
            'miji': { font: '黑体', size: 32, align: 'left', indent: 0, bold: true },
            'jinji': { font: '黑体', size: 32, align: 'left', indent: 0, bold: true },
            'jiguan': { font: '小标宋', size: 48, align: 'center', indent: 0, bold: true, color: 'red' },
            'fawenhao': { font: '仿宋', size: 32, align: 'center', indent: 0 },
            'qianfaren': { font: '仿宋', size: 32, align: 'left', indent: 0 },
            'title': { font: '小标宋', size: 44, align: 'center', indent: 0, bold: true },
            'zhusong': { font: '仿宋', size: 32, align: 'left', indent: 0 },
            'content': { font: '仿宋', size: 32, align: 'left', indent: 567 },
            'signature': { font: '仿宋', size: 32, align: 'right', indent: 0 },
            'date': { font: '仿宋', size: 32, align: 'right', indent: 0 },
            'chaosong': { font: '仿宋', size: 28, align: 'left', indent: 284 },
            'yinfa': { font: '仿宋', size: 28, align: 'left', indent: 0 },
            
            // 报告样式映射
            'greeting': { font: '仿宋', size: 32, align: 'left', indent: 0 },
            'level1': { font: '黑体', size: 32, align: 'left', indent: 0, bold: true },
            'level2': { font: '楷体', size: 32, align: 'left', indent: 567, bold: true },
            'level3': { font: '仿宋', size: 32, align: 'left', indent: 567 }
        };
        
        return styleMap[className] || { font: '仿宋', size: 32, align: 'left', indent: 567 };
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HTMLTemplateGenerator;
} else if (typeof window !== 'undefined') {
    window.HTMLTemplateGenerator = HTMLTemplateGenerator;
}
