{"name": "cowriter-backend", "version": "1.0.0", "description": "Backend service for CoWriter with OnlyOffice integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "fs-extra": "^11.1.1", "jszip": "^3.10.1", "mammoth": "^1.10.0", "multer": "^1.4.5-lts.1", "path": "^0.12.7", "pdf-parse": "^1.1.1", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["onlyoffice", "document", "editor", "backend"], "author": "CoWriter", "license": "MIT"}