{"ast": null, "code": "var _jsxFileName = \"E:\\\\k\\\\cowriter\\\\cowriter-frontend\\\\src\\\\components\\\\ProjectChat.tsx\";\nimport React from 'react';\nimport { Box, Typography, Button, Container, Paper, Stack } from '@mui/material';\nimport { Add as AddIcon, Description as DocumentIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProjectChat = ({\n  onCreateDocument,\n  onCreateTemplateDocument\n}) => {\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"md\",\n    sx: {\n      py: 4\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '60vh',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        sx: {\n          fontWeight: 600,\n          color: '#374151',\n          mb: 4\n        },\n        children: \"\\u667A\\u80FD\\u516C\\u6587\\u751F\\u6210\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 0,\n        sx: {\n          p: 6,\n          borderRadius: 3,\n          border: '2px dashed #d1d5db',\n          backgroundColor: '#f8fafc',\n          width: '100%',\n          maxWidth: 500,\n          transition: 'all 0.2s ease-in-out',\n          '&:hover': {\n            borderColor: '#6366f1',\n            backgroundColor: '#f0f9ff'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 80,\n              height: 80,\n              borderRadius: '50%',\n              backgroundColor: '#e0e7ff',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(DocumentIcon, {\n              sx: {\n                fontSize: 40,\n                color: '#6366f1'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 4,\n            lineHeight: 1.6\n          },\n          children: \"\\u521B\\u5EFA\\u65B0\\u6587\\u6863\\u5F00\\u59CB\\u667A\\u80FD\\u516C\\u6587\\u751F\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 26\n            }, this),\n            onClick: onCreateDocument,\n            sx: {\n              px: 4,\n              py: 1.5,\n              borderRadius: 2,\n              backgroundColor: '#6366f1',\n              fontSize: '1rem',\n              fontWeight: 500,\n              textTransform: 'none',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              '&:hover': {\n                backgroundColor: '#5856eb',\n                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'\n              }\n            },\n            children: \"\\u65B0\\u5EFA\\u7A7A\\u767D\\u6587\\u6863\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\uD83C\\uDFDB\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 26\n            }, this),\n            onClick: onCreateTemplateDocument,\n            sx: {\n              px: 4,\n              py: 1.5,\n              borderRadius: 2,\n              borderColor: '#6366f1',\n              color: '#6366f1',\n              fontSize: '1rem',\n              fontWeight: 500,\n              textTransform: 'none',\n              '&:hover': {\n                backgroundColor: '#f0f0ff',\n                borderColor: '#5856eb',\n                color: '#5856eb'\n              }\n            },\n            children: \"\\u57FA\\u4E8E\\u516C\\u6587\\u6A21\\u677F\\u65B0\\u5EFA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          sx: {\n            mt: 3,\n            display: 'block',\n            fontSize: '0.875rem'\n          },\n          children: \"\\u652F\\u6301\\u521B\\u5EFA\\uFF1AWord\\u6587\\u6863\\u3001Excel\\u8868\\u683C\\u3001PowerPoint\\u6F14\\u793A\\u6587\\u7A3F\\u7B49\\u591A\\u79CD\\u516C\\u6587\\u683C\\u5F0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        sx: {\n          mt: 4,\n          p: 3,\n          backgroundColor: 'white',\n          borderRadius: 2,\n          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\n          width: '100%',\n          maxWidth: 500\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flex: 1,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"primary\",\n            gutterBottom: true,\n            children: \"\\u667A\\u80FD\\u751F\\u6210\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"AI\\u81EA\\u52A8\\u751F\\u6210\\u516C\\u6587\\u5185\\u5BB9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flex: 1,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"primary\",\n            gutterBottom: true,\n            children: \"\\u683C\\u5F0F\\u89C4\\u8303\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"\\u7B26\\u5408\\u516C\\u6587\\u5199\\u4F5C\\u89C4\\u8303\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flex: 1,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"primary\",\n            gutterBottom: true,\n            children: \"\\u9AD8\\u6548\\u7F16\\u8F91\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"\\u5FEB\\u901F\\u5B8C\\u6210\\u516C\\u6587\\u7F16\\u5199\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = ProjectChat;\nexport default ProjectChat;\nvar _c;\n$RefreshReg$(_c, \"ProjectChat\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "<PERSON><PERSON>", "Container", "Paper", "<PERSON><PERSON>", "Add", "AddIcon", "Description", "DocumentIcon", "jsxDEV", "_jsxDEV", "ProjectChat", "onCreateDocument", "onCreateTemplateDocument", "max<PERSON><PERSON><PERSON>", "sx", "py", "children", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "textAlign", "variant", "component", "gutterBottom", "fontWeight", "color", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "elevation", "p", "borderRadius", "border", "backgroundColor", "width", "transition", "borderColor", "height", "fontSize", "lineHeight", "direction", "spacing", "startIcon", "onClick", "px", "textTransform", "boxShadow", "mt", "flex", "_c", "$RefreshReg$"], "sources": ["E:/k/cowriter/cowriter-frontend/src/components/ProjectChat.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Container,\n  Paper,\n  Stack,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Description as DocumentIcon,\n} from '@mui/icons-material';\n\ninterface ProjectChatProps {\n  onCreateDocument: () => void;\n  onCreateTemplateDocument?: () => void;\n}\n\nconst ProjectChat: React.FC<ProjectChatProps> = ({ onCreateDocument, onCreateTemplateDocument }) => {\n  return (\n    <Container maxWidth=\"md\" sx={{ py: 4 }}>\n      <Box\n        sx={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          minHeight: '60vh',\n          textAlign: 'center',\n        }}\n      >\n        {/* 标题 */}\n        <Typography\n          variant=\"h4\"\n          component=\"h1\"\n          gutterBottom\n          sx={{\n            fontWeight: 600,\n            color: '#374151',\n            mb: 4,\n          }}\n        >\n          智能公文生成\n        </Typography>\n\n        {/* 主要操作区域 */}\n        <Paper\n          elevation={0}\n          sx={{\n            p: 6,\n            borderRadius: 3,\n            border: '2px dashed #d1d5db',\n            backgroundColor: '#f8fafc',\n            width: '100%',\n            maxWidth: 500,\n            transition: 'all 0.2s ease-in-out',\n            '&:hover': {\n              borderColor: '#6366f1',\n              backgroundColor: '#f0f9ff',\n            },\n          }}\n        >\n          {/* 图标 */}\n          <Box\n            sx={{\n              display: 'flex',\n              justifyContent: 'center',\n              mb: 3,\n            }}\n          >\n            <Box\n              sx={{\n                width: 80,\n                height: 80,\n                borderRadius: '50%',\n                backgroundColor: '#e0e7ff',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n              }}\n            >\n              <DocumentIcon\n                sx={{\n                  fontSize: 40,\n                  color: '#6366f1',\n                }}\n              />\n            </Box>\n          </Box>\n\n          {/* 描述文字 */}\n          <Typography\n            variant=\"body1\"\n            color=\"text.secondary\"\n            sx={{ mb: 4, lineHeight: 1.6 }}\n          >\n            创建新文档开始智能公文生成\n          </Typography>\n\n          {/* 新建文档按钮组 */}\n          <Stack direction=\"row\" spacing={2}>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={onCreateDocument}\n              sx={{\n                px: 4,\n                py: 1.5,\n                borderRadius: 2,\n                backgroundColor: '#6366f1',\n                fontSize: '1rem',\n                fontWeight: 500,\n                textTransform: 'none',\n                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                '&:hover': {\n                  backgroundColor: '#5856eb',\n                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',\n                },\n              }}\n            >\n              新建空白文档\n            </Button>\n\n            <Button\n              variant=\"outlined\"\n              startIcon={<span>🏛️</span>}\n              onClick={onCreateTemplateDocument}\n              sx={{\n                px: 4,\n                py: 1.5,\n                borderRadius: 2,\n                borderColor: '#6366f1',\n                color: '#6366f1',\n                fontSize: '1rem',\n                fontWeight: 500,\n                textTransform: 'none',\n                '&:hover': {\n                  backgroundColor: '#f0f0ff',\n                  borderColor: '#5856eb',\n                  color: '#5856eb',\n                },\n              }}\n            >\n              基于公文模板新建\n            </Button>\n          </Stack>\n\n          {/* 文件格式说明 */}\n          <Typography\n            variant=\"caption\"\n            color=\"text.secondary\"\n            sx={{\n              mt: 3,\n              display: 'block',\n              fontSize: '0.875rem',\n            }}\n          >\n            支持创建：Word文档、Excel表格、PowerPoint演示文稿等多种公文格式\n          </Typography>\n        </Paper>\n\n        {/* 底部说明 */}\n        <Stack\n          direction=\"row\"\n          spacing={2}\n          sx={{\n            mt: 4,\n            p: 3,\n            backgroundColor: 'white',\n            borderRadius: 2,\n            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\n            width: '100%',\n            maxWidth: 500,\n          }}\n        >\n          <Box sx={{ flex: 1, textAlign: 'center' }}>\n            <Typography variant=\"h6\" color=\"primary\" gutterBottom>\n              智能生成\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              AI自动生成公文内容\n            </Typography>\n          </Box>\n          <Box sx={{ flex: 1, textAlign: 'center' }}>\n            <Typography variant=\"h6\" color=\"primary\" gutterBottom>\n              格式规范\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              符合公文写作规范\n            </Typography>\n          </Box>\n          <Box sx={{ flex: 1, textAlign: 'center' }}>\n            <Typography variant=\"h6\" color=\"primary\" gutterBottom>\n              高效编辑\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              快速完成公文编写\n            </Typography>\n          </Box>\n        </Stack>\n      </Box>\n    </Container>\n  );\n};\n\nexport default ProjectChat;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,KAAK,EACLC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,WAAW,IAAIC,YAAY,QACtB,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO7B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC,gBAAgB;EAAEC;AAAyB,CAAC,KAAK;EAClG,oBACEH,OAAA,CAACR,SAAS;IAACY,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eACrCP,OAAA,CAACX,GAAG;MACFgB,EAAE,EAAE;QACFG,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE;MACb,CAAE;MAAAN,QAAA,gBAGFP,OAAA,CAACV,UAAU;QACTwB,OAAO,EAAC,IAAI;QACZC,SAAS,EAAC,IAAI;QACdC,YAAY;QACZX,EAAE,EAAE;UACFY,UAAU,EAAE,GAAG;UACfC,KAAK,EAAE,SAAS;UAChBC,EAAE,EAAE;QACN,CAAE;QAAAZ,QAAA,EACH;MAED;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbvB,OAAA,CAACP,KAAK;QACJ+B,SAAS,EAAE,CAAE;QACbnB,EAAE,EAAE;UACFoB,CAAC,EAAE,CAAC;UACJC,YAAY,EAAE,CAAC;UACfC,MAAM,EAAE,oBAAoB;UAC5BC,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,MAAM;UACbzB,QAAQ,EAAE,GAAG;UACb0B,UAAU,EAAE,sBAAsB;UAClC,SAAS,EAAE;YACTC,WAAW,EAAE,SAAS;YACtBH,eAAe,EAAE;UACnB;QACF,CAAE;QAAArB,QAAA,gBAGFP,OAAA,CAACX,GAAG;UACFgB,EAAE,EAAE;YACFG,OAAO,EAAE,MAAM;YACfG,cAAc,EAAE,QAAQ;YACxBQ,EAAE,EAAE;UACN,CAAE;UAAAZ,QAAA,eAEFP,OAAA,CAACX,GAAG;YACFgB,EAAE,EAAE;cACFwB,KAAK,EAAE,EAAE;cACTG,MAAM,EAAE,EAAE;cACVN,YAAY,EAAE,KAAK;cACnBE,eAAe,EAAE,SAAS;cAC1BpB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAJ,QAAA,eAEFP,OAAA,CAACF,YAAY;cACXO,EAAE,EAAE;gBACF4B,QAAQ,EAAE,EAAE;gBACZf,KAAK,EAAE;cACT;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvB,OAAA,CAACV,UAAU;UACTwB,OAAO,EAAC,OAAO;UACfI,KAAK,EAAC,gBAAgB;UACtBb,EAAE,EAAE;YAAEc,EAAE,EAAE,CAAC;YAAEe,UAAU,EAAE;UAAI,CAAE;UAAA3B,QAAA,EAChC;QAED;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbvB,OAAA,CAACN,KAAK;UAACyC,SAAS,EAAC,KAAK;UAACC,OAAO,EAAE,CAAE;UAAA7B,QAAA,gBAChCP,OAAA,CAACT,MAAM;YACLuB,OAAO,EAAC,WAAW;YACnBuB,SAAS,eAAErC,OAAA,CAACJ,OAAO;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBe,OAAO,EAAEpC,gBAAiB;YAC1BG,EAAE,EAAE;cACFkC,EAAE,EAAE,CAAC;cACLjC,EAAE,EAAE,GAAG;cACPoB,YAAY,EAAE,CAAC;cACfE,eAAe,EAAE,SAAS;cAC1BK,QAAQ,EAAE,MAAM;cAChBhB,UAAU,EAAE,GAAG;cACfuB,aAAa,EAAE,MAAM;cACrBC,SAAS,EAAE,mCAAmC;cAC9C,SAAS,EAAE;gBACTb,eAAe,EAAE,SAAS;gBAC1Ba,SAAS,EAAE;cACb;YACF,CAAE;YAAAlC,QAAA,EACH;UAED;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETvB,OAAA,CAACT,MAAM;YACLuB,OAAO,EAAC,UAAU;YAClBuB,SAAS,eAAErC,OAAA;cAAAO,QAAA,EAAM;YAAG;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAC5Be,OAAO,EAAEnC,wBAAyB;YAClCE,EAAE,EAAE;cACFkC,EAAE,EAAE,CAAC;cACLjC,EAAE,EAAE,GAAG;cACPoB,YAAY,EAAE,CAAC;cACfK,WAAW,EAAE,SAAS;cACtBb,KAAK,EAAE,SAAS;cAChBe,QAAQ,EAAE,MAAM;cAChBhB,UAAU,EAAE,GAAG;cACfuB,aAAa,EAAE,MAAM;cACrB,SAAS,EAAE;gBACTZ,eAAe,EAAE,SAAS;gBAC1BG,WAAW,EAAE,SAAS;gBACtBb,KAAK,EAAE;cACT;YACF,CAAE;YAAAX,QAAA,EACH;UAED;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGRvB,OAAA,CAACV,UAAU;UACTwB,OAAO,EAAC,SAAS;UACjBI,KAAK,EAAC,gBAAgB;UACtBb,EAAE,EAAE;YACFqC,EAAE,EAAE,CAAC;YACLlC,OAAO,EAAE,OAAO;YAChByB,QAAQ,EAAE;UACZ,CAAE;UAAA1B,QAAA,EACH;QAED;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGRvB,OAAA,CAACN,KAAK;QACJyC,SAAS,EAAC,KAAK;QACfC,OAAO,EAAE,CAAE;QACX/B,EAAE,EAAE;UACFqC,EAAE,EAAE,CAAC;UACLjB,CAAC,EAAE,CAAC;UACJG,eAAe,EAAE,OAAO;UACxBF,YAAY,EAAE,CAAC;UACfe,SAAS,EAAE,gCAAgC;UAC3CZ,KAAK,EAAE,MAAM;UACbzB,QAAQ,EAAE;QACZ,CAAE;QAAAG,QAAA,gBAEFP,OAAA,CAACX,GAAG;UAACgB,EAAE,EAAE;YAAEsC,IAAI,EAAE,CAAC;YAAE9B,SAAS,EAAE;UAAS,CAAE;UAAAN,QAAA,gBACxCP,OAAA,CAACV,UAAU;YAACwB,OAAO,EAAC,IAAI;YAACI,KAAK,EAAC,SAAS;YAACF,YAAY;YAAAT,QAAA,EAAC;UAEtD;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvB,OAAA,CAACV,UAAU;YAACwB,OAAO,EAAC,OAAO;YAACI,KAAK,EAAC,gBAAgB;YAAAX,QAAA,EAAC;UAEnD;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNvB,OAAA,CAACX,GAAG;UAACgB,EAAE,EAAE;YAAEsC,IAAI,EAAE,CAAC;YAAE9B,SAAS,EAAE;UAAS,CAAE;UAAAN,QAAA,gBACxCP,OAAA,CAACV,UAAU;YAACwB,OAAO,EAAC,IAAI;YAACI,KAAK,EAAC,SAAS;YAACF,YAAY;YAAAT,QAAA,EAAC;UAEtD;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvB,OAAA,CAACV,UAAU;YAACwB,OAAO,EAAC,OAAO;YAACI,KAAK,EAAC,gBAAgB;YAAAX,QAAA,EAAC;UAEnD;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNvB,OAAA,CAACX,GAAG;UAACgB,EAAE,EAAE;YAAEsC,IAAI,EAAE,CAAC;YAAE9B,SAAS,EAAE;UAAS,CAAE;UAAAN,QAAA,gBACxCP,OAAA,CAACV,UAAU;YAACwB,OAAO,EAAC,IAAI;YAACI,KAAK,EAAC,SAAS;YAACF,YAAY;YAAAT,QAAA,EAAC;UAEtD;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvB,OAAA,CAACV,UAAU;YAACwB,OAAO,EAAC,OAAO;YAACI,KAAK,EAAC,gBAAgB;YAAAX,QAAA,EAAC;UAEnD;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACqB,EAAA,GAzLI3C,WAAuC;AA2L7C,eAAeA,WAAW;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}