模板识别与应用指南 - AI必读文档

本文档指导AI如何准确识别文档类型并应用对应的模板格式。

=== 文档类型识别标准 ===

## 标准公文类（使用模板.docx格式）

### 明确识别标志
**文档类型关键词：**
- 通知、请示、报告、决定、公告、通告、意见、办法、函、批复、会议纪要
- 规定、条例、细则、方案、计划（机关发布的）
- 工作安排、实施方案、管理办法

**格式特征标志：**
- 包含"关于...的通知/请示/报告"等标准公文标题格式
- 涉及发文机关、主送机关、抄送机关等公文要素
- 需要版头（份号、密级、发文字号等）
- 需要版记（抄送、印发信息等）

**发文主体特征：**
- 政府机关、事业单位、国有企业等正式机构
- 对外正式发文、政策传达、工作部署
- 具有行政效力或指导意义的文件

**内容性质特征：**
- 政策传达、工作部署、制度规定
- 对下级或相关单位的正式通知
- 需要存档备案的正式文件

### 应用模板要求
当识别为标准公文类时：
1. 必须检索"模板.docx"、"标准公文模板"相关内容
2. 严格按照版头-主体-版记三段式结构
3. 使用标准公文HTML格式标签
4. 包含完整的公文要素

## 个人公文类（使用模板1.docx格式）

### 明确识别标志
**文档类型关键词：**
- 述职报告、述廉报告、工作总结、个人总结
- 年度汇报、履职情况汇报、个人工作回顾
- 心得体会、学习总结、经验分享
- 个人工作计划、个人发展规划

**格式特征标志：**
- 以个人名义撰写的汇报性文件
- 包含个人工作回顾、成果展示、问题分析
- 使用"一、（一）、1."等层次序号结构
- 有明确的个人署名和汇报对象

**发文主体特征：**
- 个人、员工、干部、管理人员
- 向上级或组织汇报个人情况
- 个人工作总结和自我评价

**内容性质特征：**
- 个人工作成果展示
- 自我评价和反思
- 个人发展规划和改进措施
- 经验总结和心得分享

### 应用模板要求
当识别为个人公文类时：
1. 必须检索"模板1.docx"、"述职报告模板"相关内容
2. 按照标题-称谓-正文-署名结构
3. 使用述职报告HTML格式标签
4. 体现个人汇报的特点和风格

=== 识别决策流程 ===

## 步骤1：关键词分析
分析用户需求中的关键词：
- 文档类型词汇
- 发文主体描述
- 内容性质说明
- 格式要求提示

## 步骤2：特征匹配
对比标准公文类和个人公文类的特征标志：
- 文档类型是否匹配
- 发文主体是否符合
- 内容性质是否对应
- 格式要求是否一致

## 步骤3：模板选择
根据匹配结果选择对应模板：
- 标准公文类 → 模板.docx → 标准公文HTML格式
- 个人公文类 → 模板1.docx → 述职报告HTML格式

## 步骤4：格式应用
严格按照选定模板的格式要求生成内容。

=== 典型案例分析 ===

## 标准公文类案例
**用户需求：** "请生成一份关于加强办公室管理的通知"
**识别结果：** 标准公文类
**识别依据：** 
- 文档类型：通知
- 格式特征：关于...的通知
- 发文性质：机关对下级的正式通知
**应用模板：** 模板.docx
**HTML格式：** 标准公文格式（fenhao, miji, jiguan等）

## 个人公文类案例
**用户需求：** "请生成一份2024年度个人述职报告"
**识别结果：** 个人公文类
**识别依据：**
- 文档类型：述职报告
- 发文主体：个人
- 内容性质：个人工作汇报
**应用模板：** 模板1.docx
**HTML格式：** 述职报告格式（title, greeting, level1等）

## 边界案例处理
**案例：** "请生成一份部门工作总结"
**分析：** 需要进一步判断
- 如果是部门对外正式发布 → 标准公文类
- 如果是个人代表部门汇报 → 个人公文类
**处理：** 根据具体语境和用户补充信息确定

=== 强制检索要求 ===

## 检索关键词设置
**标准公文类检索词：**
- "模板.docx"
- "标准公文模板"
- "公文格式要求"
- "版头主体版记"
- "发文机关标志"

**个人公文类检索词：**
- "模板1.docx"
- "述职报告模板"
- "个人报告格式"
- "述职述廉"
- "工作总结格式"

## 检索验证标准
确认检索成功的标志：
✓ 获取到完整的模板结构信息
✓ 理解了对应的HTML格式标签
✓ 掌握了内容组织方式
✓ 明确了格式要求细节

=== 质量控制要求 ===

## 输出前检查清单
1. **类型识别正确性**
   - 文档类型判断是否准确
   - 模板选择是否合适

2. **模板应用完整性**
   - 是否检索了对应模板
   - 格式结构是否完整
   - HTML标签是否正确

3. **内容质量标准**
   - 内容是否具体详实
   - 语言是否规范专业
   - 逻辑是否清晰合理

4. **格式规范性**
   - HTML标签是否完整
   - class名称是否正确
   - 是否有占位符残留

## 错误处理机制
如果识别困难或模板检索失败：
1. 明确告知用户识别困难的原因
2. 请求用户提供更多信息
3. 说明需要的关键信息类型
4. 避免盲目猜测和错误应用

本指南是AI进行文档类型识别和模板应用的强制性标准，必须严格遵循执行。
