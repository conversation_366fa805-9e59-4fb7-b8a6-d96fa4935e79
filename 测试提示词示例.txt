优化后的Dify测试提示词示例

=== 测试用例1：标准公文类 ===

**测试输入：**
请生成一份关于加强冬季安全生产工作的通知，发文机关是市安全生产委员会，主要内容包括安全检查、应急预案、责任落实等。

**期望AI处理流程：**
1. 识别：这是标准公文类（通知类型）
2. 检索：查找"模板.docx"、"标准公文模板"相关内容
3. 应用：使用标准公文HTML格式
4. 输出：完整的div片段，包含版头-主体-版记结构

**期望输出格式：**
<div class="fenhao">000023</div>
<div class="miji">一般</div>
<div class="jinji">普通</div>
<div class="jiguan">××市安全生产委员会文件</div>
<div class="fawenhao">市安委〔2024〕8号</div>
<div class="qianfaren">签发人：李建国 王志强</div>
<div class="red-line">————————————————————————————————</div>
<div class="title">关于加强冬季安全生产工作的通知</div>
<div class="zhusong">各区县政府，市直各有关部门，各企事业单位：</div>
<div class="content">冬季是安全生产事故易发多发期，为切实做好冬季安全生产工作，有效防范和坚决遏制各类安全生产事故发生，现就有关事项通知如下：</div>
<div class="content">一、深入开展安全生产大检查。各单位要立即组织开展冬季安全生产专项检查，重点检查供暖设施、用电设备、消防器材等关键部位，发现隐患立即整改，确保不留死角。检查频次不少于每周一次，检查结果要建档备案。</div>
<div class="content">二、完善应急预案和应急准备。各单位要结合冬季特点，修订完善各类应急预案，加强应急物资储备，确保应急设备完好有效。要组织开展应急演练，提高应急处置能力，演练频次不少于每月一次。</div>
<div class="content">三、严格落实安全生产责任制。各级领导要切实履行安全生产责任，加强现场检查指导。要建立健全安全生产责任体系，明确责任人员，确保责任到人、措施到位。对责任不落实、措施不到位的，要严肃追究责任。</div>
<div class="content">各单位要高度重视冬季安全生产工作，认真贯彻落实本通知要求，确保安全生产形势持续稳定。</div>
<div class="signature">××市安全生产委员会</div>
<div class="date">2024年12月25日</div>
<div class="chaosong">抄送：省安全生产委员会，市委办公室，市政府办公室。</div>
<div class="yinfa">××市安全生产委员会办公室 2024年12月25日印发</div>

=== 测试用例2：个人公文类 ===

**测试输入：**
请生成一份办公室主任的2024年度述职述廉报告，包含履职情况、廉洁自律、存在问题和改进措施等内容。

**期望AI处理流程：**
1. 识别：这是个人公文类（述职报告类型）
2. 检索：查找"模板1.docx"、"述职报告模板"相关内容
3. 应用：使用述职报告HTML格式
4. 输出：完整的div片段，包含标题-称谓-正文-署名结构

**期望输出格式：**
<div class="title">2024年度办公室主任述职述廉报告</div>
<div class="greeting">尊敬的各位领导、同事们：</div>
<div class="content">根据组织要求，现将我担任办公室主任一年来的履职情况和廉洁自律情况向大家汇报，请予审议。</div>
<div class="level1">一、2024年度履职情况</div>
<div class="level2">（一）日常管理工作方面</div>
<div class="content">2024年，我认真履行办公室主任职责，统筹协调各项日常工作。全年共处理各类公文368份，组织大小会议42次，接待来访人员156人次，各项工作运转有序高效。建立健全了办公室工作制度15项，规范了工作流程，提高了工作效率25%。</div>
<div class="level2">（二）重点项目推进方面</div>
<div class="content">重点推进了办公自动化系统升级改造项目，投入资金80万元，实现了无纸化办公，提升了工作效率。完成了档案室标准化建设，整理归档文件2万余份，通过了省级档案工作检查。</div>
<div class="level1">二、2024年度廉洁自律情况</div>
<div class="content">严格执行中央八项规定精神，自觉遵守廉洁从政各项规定。全年未收受任何礼品礼金，未参加任何可能影响公正执行公务的宴请活动，保持了清正廉洁的良好形象。</div>
<div class="level1">三、存在的问题和不足</div>
<div class="content">通过深入反思，我认为还存在以下问题：一是理论学习还不够深入，对新形势下办公室工作的新要求理解还不够透彻；二是创新意识还需加强，工作方法还比较传统；三是与基层联系还不够紧密，了解实际情况还不够全面。</div>
<div class="level1">四、2025年工作打算和改进措施</div>
<div class="content">针对存在的问题，我将采取以下改进措施：</div>
<div class="level3">1.加强理论学习，每月至少学习党的理论知识2次，提高政治素养和业务能力，更好地适应新时代办公室工作要求。</div>
<div class="level3">2.创新工作方法，积极运用信息化手段，探索"互联网+政务服务"新模式，进一步提高工作效率和服务质量。</div>
<div class="level3">3.深入基层调研，每季度至少下基层3次，了解实际情况，听取意见建议，改进工作作风。</div>
<div class="content">以上是我的述职述廉报告，工作中还有许多不足之处，恳请各位领导和同事批评指正。我将以此次述职为新的起点，进一步改进工作，为单位发展贡献更大力量。</div>
<div class="signature">发言人：张建华</div>
<div class="date">日期：2024年12月25日</div>

=== 测试用例3：边界案例 ===

**测试输入：**
请生成一份部门工作计划。

**期望AI处理：**
AI应该询问更多信息来准确识别类型：
- 这是部门对外发布的正式工作计划吗？（标准公文类）
- 还是个人代表部门撰写的工作汇报？（个人公文类）
- 发文主体是什么？
- 主要用途是什么？

=== 验证检查点 ===

## 类型识别验证
✓ AI是否明确说明了文档类型识别结果
✓ 识别依据是否正确
✓ 模板选择是否合适

## 模板检索验证
✓ AI是否提到检索了对应的模板
✓ 是否理解了模板的格式要求
✓ 格式应用是否正确

## 输出质量验证
✓ 是否只输出div片段
✓ HTML标签是否完整正确
✓ class名称是否符合要求
✓ 内容是否具体详实
✓ 是否还有占位符

## 格式规范验证
✓ 标准公文是否包含版头-主体-版记结构
✓ 个人公文是否包含标题-称谓-正文-署名结构
✓ 序号层次是否正确
✓ 语言风格是否符合要求

=== 常见问题处理 ===

## 问题1：类型识别错误
**现象：** 将个人报告识别为标准公文
**解决：** 加强关键词识别训练，明确发文主体判断

## 问题2：模板检索失败
**现象：** 没有检索到对应模板信息
**解决：** 调整检索阈值，优化关键词设置

## 问题3：格式应用错误
**现象：** 使用了错误的HTML标签
**解决：** 强化格式要求文档，明确标签对应关系

## 问题4：仍有占位符
**现象：** 输出中包含(具体内容)等占位符
**解决：** 强调内容具体化要求，禁止任何形式的占位符

通过这些测试用例可以全面验证优化后的提示词效果。
