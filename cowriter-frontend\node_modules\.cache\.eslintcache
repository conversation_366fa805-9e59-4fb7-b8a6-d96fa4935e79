[{"E:\\k\\cowriter\\cowriter-frontend\\src\\index.tsx": "1", "E:\\k\\cowriter\\cowriter-frontend\\src\\reportWebVitals.ts": "2", "E:\\k\\cowriter\\cowriter-frontend\\src\\App.tsx": "3", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\Sidebar.tsx": "4", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\DocumentEditor.tsx": "5", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\AIChat.tsx": "6", "E:\\k\\cowriter\\cowriter-frontend\\src\\config\\onlyoffice.ts": "7", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\OnlyOfficeTest.tsx": "8", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\SimpleOnlyOfficeTest.tsx": "9", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\OnlyOfficeEditor.tsx": "10", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\ProjectChat.tsx": "11", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\PDFChat.tsx": "12", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\ResizableHandle.tsx": "13", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\GeneralChat.tsx": "14", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\Settings.tsx": "15", "E:\\k\\cowriter\\cowriter-frontend\\src\\services\\difyService.ts": "16", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\DifyTest.tsx": "17", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\WorkflowConfig.tsx": "18", "E:\\k\\cowriter\\cowriter-frontend\\src\\utils\\fileProcessor.ts": "19", "E:\\k\\cowriter\\cowriter-frontend\\src\\utils\\pdfExtractor.ts": "20", "E:\\k\\cowriter\\cowriter-frontend\\src\\config\\api.ts": "21", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\MarkdownRenderer.tsx": "22", "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\KnowledgeManager.tsx": "23", "E:\\k\\cowriter\\cowriter-frontend\\src\\services\\difyKnowledgeService.ts": "24"}, {"size": 554, "mtime": 1752474988885, "results": "25", "hashOfConfig": "26"}, {"size": 425, "mtime": 1752474987304, "results": "27", "hashOfConfig": "26"}, {"size": 22955, "mtime": 1755840771133, "results": "28", "hashOfConfig": "26"}, {"size": 15043, "mtime": 1753359346663, "results": "29", "hashOfConfig": "26"}, {"size": 13105, "mtime": 1752721845181, "results": "30", "hashOfConfig": "26"}, {"size": 52191, "mtime": 1755526089589, "results": "31", "hashOfConfig": "26"}, {"size": 4179, "mtime": 1755840771118, "results": "32", "hashOfConfig": "26"}, {"size": 9046, "mtime": 1752477601202, "results": "33", "hashOfConfig": "26"}, {"size": 5711, "mtime": 1752477685002, "results": "34", "hashOfConfig": "26"}, {"size": 29689, "mtime": 1752655139665, "results": "35", "hashOfConfig": "26"}, {"size": 4798, "mtime": 1755840771132, "results": "36", "hashOfConfig": "26"}, {"size": 6272, "mtime": 1752512943480, "results": "37", "hashOfConfig": "26"}, {"size": 2034, "mtime": 1752518171425, "results": "38", "hashOfConfig": "26"}, {"size": 32130, "mtime": 1753883366084, "results": "39", "hashOfConfig": "26"}, {"size": 14233, "mtime": 1752562127339, "results": "40", "hashOfConfig": "26"}, {"size": 20896, "mtime": 1755231885888, "results": "41", "hashOfConfig": "26"}, {"size": 5727, "mtime": 1752577544202, "results": "42", "hashOfConfig": "26"}, {"size": 8339, "mtime": 1752582933143, "results": "43", "hashOfConfig": "26"}, {"size": 6960, "mtime": 1752568808817, "results": "44", "hashOfConfig": "26"}, {"size": 2245, "mtime": 1752695454423, "results": "45", "hashOfConfig": "26"}, {"size": 2393, "mtime": 1752729484713, "results": "46", "hashOfConfig": "26"}, {"size": 4395, "mtime": 1753189200082, "results": "47", "hashOfConfig": "26"}, {"size": 51179, "mtime": 1753885542989, "results": "48", "hashOfConfig": "26"}, {"size": 13868, "mtime": 1753366018923, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wlz0dh", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\k\\cowriter\\cowriter-frontend\\src\\index.tsx", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\reportWebVitals.ts", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\App.tsx", ["122", "123"], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\Sidebar.tsx", ["124", "125"], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\DocumentEditor.tsx", ["126"], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\AIChat.tsx", ["127", "128", "129", "130"], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\config\\onlyoffice.ts", ["131", "132"], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\OnlyOfficeTest.tsx", ["133"], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\SimpleOnlyOfficeTest.tsx", ["134", "135"], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\OnlyOfficeEditor.tsx", ["136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158"], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\ProjectChat.tsx", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\PDFChat.tsx", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\ResizableHandle.tsx", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\GeneralChat.tsx", ["159", "160", "161", "162", "163", "164", "165", "166"], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\Settings.tsx", ["167", "168", "169", "170", "171", "172"], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\services\\difyService.ts", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\DifyTest.tsx", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\WorkflowConfig.tsx", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\utils\\fileProcessor.ts", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\utils\\pdfExtractor.ts", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\config\\api.ts", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\MarkdownRenderer.tsx", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\components\\KnowledgeManager.tsx", [], [], "E:\\k\\cowriter\\cowriter-frontend\\src\\services\\difyKnowledgeService.ts", [], [], {"ruleId": "173", "severity": 1, "message": "174", "line": 4, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 4, "endColumn": 18}, {"ruleId": "173", "severity": 1, "message": "177", "line": 14, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 14, "endColumn": 21}, {"ruleId": "173", "severity": 1, "message": "178", "line": 14, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 14, "endColumn": 9}, {"ruleId": "173", "severity": 1, "message": "179", "line": 23, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 23, "endColumn": 7}, {"ruleId": "180", "severity": 1, "message": "181", "line": 61, "column": 6, "nodeType": "182", "endLine": 61, "endColumn": 35, "suggestions": "183"}, {"ruleId": "173", "severity": 1, "message": "184", "line": 19, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 19, "endColumn": 14}, {"ruleId": "180", "severity": 1, "message": "185", "line": 128, "column": 6, "nodeType": "182", "endLine": 128, "endColumn": 53, "suggestions": "186"}, {"ruleId": "187", "severity": 1, "message": "188", "line": 560, "column": 49, "nodeType": "189", "messageId": "190", "endLine": 560, "endColumn": 50, "suggestions": "191"}, {"ruleId": "173", "severity": 1, "message": "192", "line": 577, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 577, "endColumn": 27}, {"ruleId": "173", "severity": 1, "message": "193", "line": 130, "column": 11, "nodeType": "175", "messageId": "176", "endLine": 130, "endColumn": 19}, {"ruleId": "173", "severity": 1, "message": "193", "line": 144, "column": 11, "nodeType": "175", "messageId": "176", "endLine": 144, "endColumn": 19}, {"ruleId": "173", "severity": 1, "message": "194", "line": 126, "column": 13, "nodeType": "175", "messageId": "176", "endLine": 126, "endColumn": 27}, {"ruleId": "173", "severity": 1, "message": "193", "line": 39, "column": 13, "nodeType": "175", "messageId": "176", "endLine": 39, "endColumn": 21}, {"ruleId": "173", "severity": 1, "message": "193", "line": 58, "column": 13, "nodeType": "175", "messageId": "176", "endLine": 58, "endColumn": 21}, {"ruleId": "173", "severity": 1, "message": "195", "line": 4, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 4, "endColumn": 9}, {"ruleId": "173", "severity": 1, "message": "196", "line": 7, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 7, "endColumn": 7}, {"ruleId": "173", "severity": 1, "message": "197", "line": 8, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 8, "endColumn": 14}, {"ruleId": "173", "severity": 1, "message": "198", "line": 9, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 9, "endColumn": 7}, {"ruleId": "173", "severity": 1, "message": "199", "line": 10, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 10, "endColumn": 11}, {"ruleId": "173", "severity": 1, "message": "200", "line": 11, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 11, "endColumn": 15}, {"ruleId": "173", "severity": 1, "message": "201", "line": 12, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 12, "endColumn": 15}, {"ruleId": "173", "severity": 1, "message": "202", "line": 13, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 13, "endColumn": 13}, {"ruleId": "173", "severity": 1, "message": "203", "line": 15, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 15, "endColumn": 19}, {"ruleId": "173", "severity": 1, "message": "204", "line": 16, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 16, "endColumn": 7}, {"ruleId": "173", "severity": 1, "message": "205", "line": 20, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 20, "endColumn": 9}, {"ruleId": "173", "severity": 1, "message": "206", "line": 21, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 21, "endColumn": 7}, {"ruleId": "173", "severity": 1, "message": "207", "line": 22, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 22, "endColumn": 9}, {"ruleId": "173", "severity": 1, "message": "208", "line": 23, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 23, "endColumn": 6}, {"ruleId": "173", "severity": 1, "message": "209", "line": 51, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 51, "endColumn": 15}, {"ruleId": "173", "severity": 1, "message": "210", "line": 52, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 52, "endColumn": 19}, {"ruleId": "173", "severity": 1, "message": "211", "line": 54, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 54, "endColumn": 22}, {"ruleId": "173", "severity": 1, "message": "212", "line": 214, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 214, "endColumn": 25}, {"ruleId": "173", "severity": 1, "message": "213", "line": 246, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 246, "endColumn": 25}, {"ruleId": "173", "severity": 1, "message": "214", "line": 622, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 622, "endColumn": 20}, {"ruleId": "173", "severity": 1, "message": "215", "line": 636, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 636, "endColumn": 23}, {"ruleId": "173", "severity": 1, "message": "216", "line": 645, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 645, "endColumn": 25}, {"ruleId": "180", "severity": 1, "message": "217", "line": 738, "column": 6, "nodeType": "182", "endLine": 738, "endColumn": 18, "suggestions": "218"}, {"ruleId": "173", "severity": 1, "message": "219", "line": 9, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 9, "endColumn": 9}, {"ruleId": "173", "severity": 1, "message": "220", "line": 10, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 10, "endColumn": 11}, {"ruleId": "173", "severity": 1, "message": "221", "line": 11, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 11, "endColumn": 14}, {"ruleId": "173", "severity": 1, "message": "222", "line": 13, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 13, "endColumn": 7}, {"ruleId": "173", "severity": 1, "message": "223", "line": 14, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 14, "endColumn": 8}, {"ruleId": "173", "severity": 1, "message": "203", "line": 16, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 16, "endColumn": 19}, {"ruleId": "173", "severity": 1, "message": "224", "line": 21, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 21, "endColumn": 20}, {"ruleId": "173", "severity": 1, "message": "225", "line": 26, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 26, "endColumn": 13}, {"ruleId": "173", "severity": 1, "message": "226", "line": 17, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 17, "endColumn": 10}, {"ruleId": "173", "severity": 1, "message": "221", "line": 18, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 18, "endColumn": 14}, {"ruleId": "173", "severity": 1, "message": "227", "line": 19, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 19, "endColumn": 13}, {"ruleId": "173", "severity": 1, "message": "219", "line": 20, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 20, "endColumn": 9}, {"ruleId": "173", "severity": 1, "message": "220", "line": 21, "column": 3, "nodeType": "175", "messageId": "176", "endLine": 21, "endColumn": 11}, {"ruleId": "180", "severity": 1, "message": "228", "line": 88, "column": 6, "nodeType": "182", "endLine": 88, "endColumn": 8, "suggestions": "229"}, "@typescript-eslint/no-unused-vars", "'Settings' is defined but never used.", "Identifier", "unusedVar", "'difyService' is defined but never used.", "'Folder' is defined but never used.", "'Menu' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'onDocumentStateChange'. Either include it or remove the dependency array. If 'onDocumentStateChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["230"], "'AddToPhotos' is defined but never used.", "React Hook useEffect has a missing dependency: 'getInitialMessage'. Either include it or remove the dependency array.", ["231"], "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["232", "233"], "'generateAIResponse' is assigned a value but never used.", "'response' is assigned a value but never used.", "'healthResponse' is assigned a value but never used.", "'Button' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'ListItemIcon' is defined but never used.", "'IconButton' is defined but never used.", "'CircularProgress' is defined but never used.", "'Chip' is defined but never used.", "'Delete' is defined but never used.", "'Edit' is defined but never used.", "'Upload' is defined but never used.", "'Add' is defined but never used.", "'files' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'selectedFile' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "'handleDeleteFile' is assigned a value but never used.", "'closeEditor' is assigned a value but never used.", "'formatFileSize' is assigned a value but never used.", "'getFileTypeColor' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'openEditor'. Either include it or remove the dependency array.", ["234"], "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormControl' is defined but never used.", "'Fade' is defined but never used.", "'Slide' is defined but never used.", "'KeyboardArrowDown' is defined but never used.", "'Psychology' is defined but never used.", "'Divider' is defined but never used.", "'InputLabel' is defined but never used.", "React Hook useEffect has a missing dependency: 'initializeDefaultPrompts'. Either include it or remove the dependency array.", ["235"], {"desc": "236", "fix": "237"}, {"desc": "238", "fix": "239"}, {"messageId": "240", "fix": "241", "desc": "242"}, {"messageId": "243", "fix": "244", "desc": "245"}, {"desc": "246", "fix": "247"}, {"desc": "248", "fix": "249"}, "Update the dependencies array to be: [onDocumentStateChange, selectedDocumentFromSidebar]", {"range": "250", "text": "251"}, "Update the dependencies array to be: [getInitialMessage, selectedDocument?.id, selectedDocument?.title]", {"range": "252", "text": "253"}, "removeEscape", {"range": "254", "text": "255"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "256", "text": "257"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [documentId, openEditor]", {"range": "258", "text": "259"}, "Update the dependencies array to be: [initializeDefaultPrompts]", {"range": "260", "text": "261"}, [1590, 1619], "[onDocumentStateChange, selectedDocumentFromSidebar]", [3432, 3479], "[getInitialMessage, selectedDocument?.id, selectedDocument?.title]", [16717, 16718], "", [16717, 16717], "\\", [23489, 23501], "[documentId, openEditor]", [1970, 1972], "[initializeDefaultPrompts]"]