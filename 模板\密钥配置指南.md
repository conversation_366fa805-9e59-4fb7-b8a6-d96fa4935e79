# OnlyOffice AI插件密钥配置指南

## 🔑 **密钥获取方法**

### **1. 获取Dify API密钥**

#### **方法1：从Dify管理界面获取**
1. 登录您的Dify管理后台
2. 进入"应用"或"工作流"页面
3. 选择对应的应用/工作流
4. 点击"API访问"或"API密钥"
5. 复制API密钥

#### **方法2：查看现有配置**
如果您已经在cowriter项目中配置了Dify，可以查看现有的API密钥：

```bash
# 查看前端配置文件
grep -r "app-" src/
# 或查看环境变量
cat .env
```

### **2. 常用的Dify API密钥格式**
- 工作流密钥：`app-xxxxxxxxxxxxxxxxxxxxxxxxx`
- 应用密钥：`app-xxxxxxxxxxxxxxxxxxxxxxxxx`
- 长度通常为32-40个字符

## 🔧 **在OnlyOffice中配置密钥**

### **方法1：通过AI插件界面配置**

1. **打开OnlyOffice文档编辑器**
2. **点击"插件"菜单**
3. **选择"AI"插件**
4. **点击设置或配置按钮**
5. **选择"Dify"作为提供商**
6. **在API密钥字段中输入您的Dify API密钥**

### **方法2：通过插件管理器配置**

1. **打开插件管理器**
2. **找到AI插件**
3. **点击配置或设置**
4. **添加新的提供商配置**：
   - 提供商名称：Dify
   - API密钥：您的Dify API密钥
   - 基础URL：http://localhost/v1

## 📋 **推荐的密钥配置方案**

### **方案1：使用公文写作工作流密钥（推荐）**

如果您主要用于公文写作，建议使用公文写作工作流的API密钥：

1. **获取密钥**：
   - 在Dify中找到工作流ID为 `prHddcqyPKtVcPiY` 的公文写作工作流
   - 复制其API密钥

2. **配置说明**：
   - 这个密钥可以访问您优化过的公文写作工作流
   - 支持HTML格式输出
   - 包含模板识别功能

### **方案2：使用通用聊天应用密钥**

如果您需要更灵活的对话功能：

1. **获取密钥**：
   - 在Dify中找到应用ID为 `e7afaa18-1258-4370-ad2b-de107155aa85` 的通用聊天应用
   - 复制其API密钥

2. **配置说明**：
   - 支持多轮对话
   - 更灵活的交互方式

## 🔍 **密钥测试方法**

### **测试API连接**

配置完密钥后，可以通过以下方式测试：

#### **方法1：在OnlyOffice中测试**
1. 打开AI插件
2. 选择Dify提供商
3. 输入简单的测试问题："你好"
4. 查看是否有正常响应

#### **方法2：使用curl命令测试**
```bash
# 测试公文写作工作流
curl -X POST http://localhost/v1/workflows/prHddcqyPKtVcPiY/run \
  -H "Authorization: Bearer 您的API密钥" \
  -H "Content-Type: application/json" \
  -d '{
    "inputs": {
      "query": "请生成一份测试通知"
    },
    "response_mode": "blocking",
    "user": "test"
  }'

# 测试通用聊天应用
curl -X POST http://localhost/v1/chat-messages \
  -H "Authorization: Bearer 您的API密钥" \
  -H "Content-Type: application/json" \
  -d '{
    "inputs": {},
    "query": "你好",
    "response_mode": "blocking",
    "conversation_id": "",
    "user": "test"
  }'
```

## ⚠️ **常见问题和解决方案**

### **问题1：密钥无效**
**症状**：提示"API密钥无效"或"认证失败"
**解决方案**：
1. 检查密钥是否完整复制
2. 确认密钥没有过期
3. 验证密钥对应的应用/工作流是否正常运行

### **问题2：连接超时**
**症状**：请求超时或无响应
**解决方案**：
1. 检查Dify服务是否正常运行
2. 确认网络连接
3. 检查防火墙设置

### **问题3：响应格式错误**
**症状**：收到响应但格式不正确
**解决方案**：
1. 检查Dify工作流配置
2. 确认提示词设置正确
3. 验证HTML输出格式

## 🚀 **高级配置**

### **多密钥配置**

如果您想为不同功能使用不同的密钥，可以：

1. **创建多个提供商配置**：
   - Dify-公文写作
   - Dify-文件解读
   - Dify-通用聊天

2. **为每个配置设置对应的API密钥**

### **环境变量配置**

在生产环境中，建议使用环境变量管理密钥：

```bash
# 设置环境变量
export DIFY_DOCUMENT_API_KEY="app-your-document-key"
export DIFY_CHAT_API_KEY="app-your-chat-key"
```

## 📞 **获取帮助**

如果在配置过程中遇到问题：

1. **检查Dify服务状态**：确认Dify服务正常运行
2. **查看日志**：检查OnlyOffice和Dify的错误日志
3. **验证网络**：确认OnlyOffice可以访问Dify服务
4. **测试API**：使用curl命令直接测试API连接

配置成功后，您就可以在OnlyOffice中直接使用Dify的AI服务了！🎯
